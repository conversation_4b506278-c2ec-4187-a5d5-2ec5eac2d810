'use client';

import React, { useState, useEffect } from 'react';
import { Card, Empty, Spin, Button, Avatar, Select, Input, Row, Col, Space } from 'antd';
import { Eye, Filter, Search, User, Calendar, BookOpen } from 'lucide-react';
import Image from 'next/image';
import styles from './ClassProjects.module.css';
import {
  fetchTeacherClassesForProjects,
  fetchClassProjects,
  fetchAllClassProjects,
  handleViewWork,
  initClassProjectsState,
  formatPublishTime,
  fixProjectImageUrl,
  Project,
  ClassProjectsState
} from '../utils/classProjectsUtils';
import { ClassInfo } from '../utils/types';
import { GetNotification } from '../utils/notificationUtils';

const { Option } = Select;
const { Search: SearchInput } = Input;

interface School {
  id: number;
  schoolName: string;
  province: string;
  city: string;
  district: string;
}

interface ClassProjectsProps {
  selectedSchool?: School | null;
}

const ClassProjects = ({ selectedSchool }: ClassProjectsProps) => {
  // 状态管理
  const [state, setState] = useState<ClassProjectsState>(initClassProjectsState());
  const notification = GetNotification();

  // 更新状态的辅助函数
  const updateState = (updates: Partial<ClassProjectsState>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  // 获取班级列表
  const loadClasses = async () => {
    updateState({ classesLoading: true });
    try {
      const schoolId = selectedSchool?.id;
      if (!schoolId) {
        console.warn('没有选择学校，无法获取班级列表');
        updateState({ classes: [] });
        return;
      }

      const classList = await fetchTeacherClassesForProjects(schoolId);
      updateState({ classes: classList });
      console.log('成功加载班级列表，数量:', classList.length);
    } catch (error) {
      console.error('获取班级列表失败:', error);
      notification.error('获取班级列表失败');
    } finally {
      updateState({ classesLoading: false });
    }
  };

  // 获取项目列表
  const loadProjects = async (classId?: number, searchKeyword?: string) => {
    updateState({ loading: true });
    try {
      let projectList: Project[] = [];

      if (classId) {
        // 获取特定班级的项目
        projectList = await fetchClassProjects(classId, state.orderBy, searchKeyword);
      } else {
        // 获取所有班级的项目
        projectList = await fetchAllClassProjects(state.classes, state.orderBy, searchKeyword);
      }

      updateState({ projects: projectList });
      console.log('成功加载项目列表，数量:', projectList.length);
    } catch (error) {
      console.error('获取项目列表失败:', error);
      notification.error('获取项目列表失败');
    } finally {
      updateState({ loading: false });
    }
  };

  // 处理班级选择
  const handleClassChange = (classId: number | null) => {
    updateState({ selectedClassId: classId });
    loadProjects(classId || undefined, state.keyword);
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    updateState({ keyword: value });
    loadProjects(state.selectedClassId || undefined, value);
  };

  // 处理排序方式改变
  const handleOrderByChange = (orderBy: 'newest' | 'oldest' | 'popular') => {
    updateState({ orderBy });
    loadProjects(state.selectedClassId || undefined, state.keyword);
  };

  // 处理查看项目
  const handleViewProject = async (project: Project) => {
    try {
      await handleViewWork(project.id);
    } catch (error) {
      console.error('查看项目失败:', error);
      notification.error('查看项目失败');
    }
  };

  // 监听学校变化
  useEffect(() => {
    if (selectedSchool) {
      console.log('学校变化，重新加载班级列表:', selectedSchool.schoolName);
      loadClasses();
    }
  }, [selectedSchool]);

  // 组件挂载时加载数据
  useEffect(() => {
    if (selectedSchool) {
      loadClasses();
    }
  }, []);

  // 班级列表变化时加载项目
  useEffect(() => {
    if (state.classes.length > 0) {
      loadProjects(state.selectedClassId || undefined, state.keyword);
    }
  }, [state.classes]);

  // 如果没有选择学校，显示提示
  if (!selectedSchool) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <h2 className={styles.title}>
              <BookOpen className={styles.titleIcon} />
              班级项目
            </h2>
            <p className={styles.subtitle}>查看和管理班级学生的项目作品</p>
          </div>
        </div>
        <div className={styles.content}>
          <div className={styles.emptyContainer}>
            <Empty
              description={
                <div className={styles.emptyDescription}>
                  <p>请先在左侧选择一个学校</p>
                  <p className={styles.emptyHint}>选择学校后即可查看该学校的班级项目</p>
                </div>
              }
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* 头部控制区域 */}
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <h2 className={styles.title}>
            <BookOpen className={styles.titleIcon} />
            班级项目 - {selectedSchool.schoolName}
          </h2>
          <p className={styles.subtitle}>
            查看和管理所有班级的学生项目作品
          </p>
        </div>
      </div>

      {/* 筛选控制区域 */}
      <div className={styles.controls}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8} lg={6}>
            <div className={styles.controlItem}>
              <label className={styles.controlLabel}>选择班级:</label>
              <Select
                placeholder="全部班级"
                value={state.selectedClassId}
                onChange={handleClassChange}
                loading={state.classesLoading}
                allowClear
                className={styles.classSelect}
              >
                {state.classes.map((classInfo) => (
                  <Option key={classInfo.id} value={classInfo.id}>
                    {classInfo.grade} - {classInfo.className}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <div className={styles.controlItem}>
              <label className={styles.controlLabel}>排序方式:</label>
              <Select
                value={state.orderBy}
                onChange={handleOrderByChange}
                className={styles.orderSelect}
              >
                <Option value="newest">最新发布</Option>
                <Option value="oldest">最早发布</Option>
                <Option value="popular">最受欢迎</Option>
              </Select>
            </div>
          </Col>

          <Col xs={24} sm={24} md={8} lg={12}>
            <div className={styles.controlItem}>
              <SearchInput
                placeholder="搜索项目标题或描述..."
                allowClear
                onSearch={handleSearch}
                onChange={(e) => {
                  if (!e.target.value) {
                    handleSearch('');
                  }
                }}
                className={styles.searchInput}
                prefix={<Search size={16} />}
              />
            </div>
          </Col>
        </Row>
      </div>

      {/* 项目列表内容区域 */}
      <div className={styles.content}>
        {state.loading ? (
          <div className={styles.loadingContainer}>
            <Spin size="large" />
            <p className={styles.loadingText}>正在加载项目...</p>
          </div>
        ) : state.projects.length > 0 ? (
          <div className={styles.projectGrid}>
            {state.projects.map((project) => (
              <ProjectCard
                key={project.id}
                project={project}
                onView={handleViewProject}
              />
            ))}
          </div>
        ) : (
          <div className={styles.emptyContainer}>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div className={styles.emptyDescription}>
                  <p>暂无项目数据</p>
                  <p className={styles.emptyHint}>
                    {state.selectedClassId
                      ? '该班级还没有发布任何项目作品'
                      : '您的班级还没有发布任何项目作品'
                    }
                  </p>
                </div>
              }
            />
          </div>
        )}
      </div>
    </div>
  );
};

// 项目卡片组件
interface ProjectCardProps {
  project: Project;
  onView: (project: Project) => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project, onView }) => {
  return (
    <Card
      className={styles.projectCard}
      hoverable
      onClick={() => onView(project)}
      cover={
        <div className={styles.projectCover}>
          <Image
            src={fixProjectImageUrl(project.coverImage || project.screenShotImage)}
            alt={project.title}
            width={300}
            height={200}
            className={styles.projectImage}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = "/images/xiaoluo-default.webp";
            }}
          />
          <div className={styles.projectOverlay}>
            <Button
              type="primary"
              icon={<Eye size={16} />}
              className={styles.viewButton}
            >
              查看项目
            </Button>
          </div>
        </div>
      }
    >
      {/* 项目信息 */}
      <div className={styles.projectInfo}>
        <h3 className={styles.projectTitle} title={project.title}>
          {project.title}
        </h3>

        {project.description && (
          <p className={styles.projectDescription} title={project.description}>
            {project.description}
          </p>
        )}

        {/* 作者和班级信息 */}
        <div className={styles.projectMeta}>
          <div className={styles.authorInfo}>
            <Avatar
              size={32}
              src={fixProjectImageUrl(project.author?.avatarUrl)}
              className={styles.authorAvatar}
            >
              {project.author?.nickName?.[0] || 'U'}
            </Avatar>
            <span className={styles.authorName}>
              {project.author?.nickName || '未知用户'}
            </span>
          </div>

          {/* 班级信息 */}
          {(project.className || project.grade) && (
            <div className={styles.classInfo}>
              <div className={styles.classTag}>
                {project.grade && project.className
                  ? `${project.grade} - ${project.className}`
                  : project.className || project.grade
                }
              </div>
            </div>
          )}
        </div>

        {/* 底部信息 */}
        <div className={styles.projectFooter}>
          <div className={styles.publishTime}>
            <Calendar size={14} />
            <span>{formatPublishTime(project.publishToClassTime)}</span>
          </div>

          <div className={styles.viewCount}>
            <Eye size={14} />
            <span>{project.viewCount || 0}</span>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ClassProjects;
