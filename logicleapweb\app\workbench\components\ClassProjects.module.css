/* 班级项目组件样式 - 现代化设计优化 */

.container {
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

/* 头部区域 - 现代化设计 */
.header {
  margin-bottom: 28px;
  padding: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.headerLeft {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.title {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  letter-spacing: -0.025em;
}

.titleIcon {
  color: #4f46e5;
  background: linear-gradient(135deg, #ddd6fe, #c7d2fe);
  padding: 12px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
}

.subtitle {
  margin: 0;
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
}

/* 控制区域 - 现代化卡片设计 */
.controls {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 28px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 28px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
}

.controlItem {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.controlLabel {
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: 0.025em;
}

.classSelect,
.orderSelect {
  width: 100%;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.classSelect:hover,
.orderSelect:hover {
  border-color: #a5b4fc;
  box-shadow: 0 0 0 3px rgba(165, 180, 252, 0.1);
}

.classSelect:focus,
.orderSelect:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.searchInput {
  width: 100%;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.searchInput:hover {
  border-color: #a5b4fc;
  box-shadow: 0 0 0 3px rgba(165, 180, 252, 0.1);
}

.searchInput:focus-within {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 内容区域 - 现代化设计 */
.content {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  min-height: 500px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

/* 加载状态 - 优化设计 */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
  gap: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.loadingText {
  margin: 0;
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* 空状态 - 现代化设计 */
.emptyContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.emptyDescription {
  text-align: center;
}

.emptyDescription p {
  margin: 0;
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
}

.emptyHint {
  font-size: 14px !important;
  margin-top: 12px !important;
  color: #94a3b8 !important;
}

/* 项目网格 - 优化布局 */
.projectGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 24px;
  padding: 32px;
  background: transparent;
}

/* 项目卡片 - 现代化设计 */
.projectCard {
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  position: relative;
}

.projectCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  padding: 2px;
  background: linear-gradient(135deg, #4f46e5, #7c3aed, #ec4899);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.projectCard:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(79, 70, 229, 0.15);
}

.projectCard:hover::before {
  opacity: 1;
}

.projectCover {
  position: relative;
  overflow: hidden;
  height: 220px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.projectImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.projectCard:hover .projectImage {
  transform: scale(1.08);
  filter: brightness(1.1) saturate(1.2);
}

.projectOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.8) 0%, rgba(124, 58, 237, 0.8) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(4px);
}

.projectCard:hover .projectOverlay {
  opacity: 1;
}

.viewButton {
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #4f46e5;
  border: 2px solid #ffffff;
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.viewButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 255, 255, 0.4);
  background: #ffffff;
}

/* 项目信息 - 现代化设计 */
.projectInfo {
  padding: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.projectTitle {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  letter-spacing: -0.025em;
}

.projectDescription {
  margin: 0 0 20px 0;
  font-size: 15px;
  color: #64748b;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-weight: 400;
}

/* 项目元信息 - 优化布局 */
.projectMeta {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.authorInfo {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.authorAvatar {
  flex-shrink: 0;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.authorName {
  font-size: 15px;
  color: #1e293b;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.classInfo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.classTag {
  margin: 0;
  font-size: 13px;
  border-radius: 8px;
  padding: 6px 12px;
  font-weight: 600;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #3b82f6;
  color: #1e40af;
  letter-spacing: 0.025em;
}

/* 项目底部 - 现代化设计 */
.projectFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0 0 0;
  border-top: 2px solid #f1f5f9;
  margin-top: 4px;
}

.publishTime,
.viewCount {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #64748b;
  font-weight: 500;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

/* 响应式设计 - 优化移动端体验 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .header {
    padding: 20px;
    margin-bottom: 20px;
  }

  .title {
    font-size: 24px;
    gap: 12px;
  }

  .titleIcon {
    padding: 10px;
  }

  .subtitle {
    font-size: 14px;
  }

  .controls {
    padding: 20px;
    margin-bottom: 20px;
  }

  .projectGrid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 20px;
  }

  .projectCover {
    height: 180px;
  }

  .projectInfo {
    padding: 20px;
  }

  .projectTitle {
    font-size: 16px;
  }

  .projectDescription {
    font-size: 14px;
  }

  .projectMeta {
    gap: 12px;
  }

  .authorInfo {
    padding: 10px 12px;
  }

  .authorName {
    font-size: 14px;
  }

  .classTag {
    font-size: 12px;
    padding: 4px 8px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 12px;
  }

  .header {
    padding: 16px;
    margin-bottom: 16px;
  }

  .title {
    font-size: 20px;
    gap: 10px;
  }

  .titleIcon {
    padding: 8px;
  }

  .subtitle {
    font-size: 13px;
  }

  .controls {
    padding: 16px;
    margin-bottom: 16px;
  }

  .projectGrid {
    padding: 16px;
    gap: 16px;
  }

  .projectCover {
    height: 160px;
  }

  .projectInfo {
    padding: 16px;
  }

  .projectTitle {
    font-size: 15px;
  }

  .projectDescription {
    font-size: 13px;
  }

  .authorInfo {
    padding: 8px 10px;
    gap: 8px;
  }

  .authorName {
    font-size: 13px;
  }

  .publishTime,
  .viewCount {
    font-size: 12px;
    padding: 6px 8px;
  }
}

/* 超大屏幕优化 */
@media (min-width: 1400px) {
  .projectGrid {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 32px;
    padding: 40px;
  }

  .projectCover {
    height: 240px;
  }

  .projectTitle {
    font-size: 20px;
  }

  .projectDescription {
    font-size: 16px;
  }
}

/* 动画效果增强 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.projectCard {
  animation: fadeInUp 0.6s ease-out;
}

.projectCard:nth-child(even) {
  animation-delay: 0.1s;
}

.projectCard:nth-child(3n) {
  animation-delay: 0.2s;
}
