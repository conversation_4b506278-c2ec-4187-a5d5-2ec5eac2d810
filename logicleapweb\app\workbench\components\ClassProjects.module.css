/* 班级项目组件样式 - 现代化设计优化 */

.container {
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

/* 头部区域 - 现代化设计 */
.header {
  margin-bottom: 28px;
  padding: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.headerLeft {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.title {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  letter-spacing: -0.025em;
}

.titleIcon {
  color: #4f46e5;
  background: linear-gradient(135deg, #ddd6fe, #c7d2fe);
  padding: 12px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
}

.subtitle {
  margin: 0;
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
}

/* 控制区域 - 现代化卡片设计 */
.controls {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 28px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 28px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
}

.controlItem {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.controlLabel {
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: 0.025em;
}

.classSelect,
.orderSelect {
  width: 100%;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.classSelect:hover,
.orderSelect:hover {
  border-color: #a5b4fc;
  box-shadow: 0 0 0 3px rgba(165, 180, 252, 0.1);
}

.classSelect:focus,
.orderSelect:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.searchInput {
  width: 100%;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.searchInput:hover {
  border-color: #a5b4fc;
  box-shadow: 0 0 0 3px rgba(165, 180, 252, 0.1);
}

.searchInput:focus-within {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 内容区域 - 现代化设计 */
.content {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  min-height: 500px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

/* 加载状态 - 优化设计 */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
  gap: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.loadingText {
  margin: 0;
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* 空状态 - 现代化设计 */
.emptyContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.emptyDescription {
  text-align: center;
}

.emptyDescription p {
  margin: 0;
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
}

.emptyHint {
  font-size: 14px !important;
  margin-top: 12px !important;
  color: #94a3b8 !important;
}

/* 项目网格 - 紧凑专业布局 */
.projectGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  padding: 20px;
  background: transparent;
}

/* 项目卡片 - 简约专业设计 */
.projectCard {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
}

.projectCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #c7d2fe;
}

.projectCover {
  position: relative;
  overflow: hidden;
  height: 160px;
  background: #f8fafc;
}

.projectImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.projectCard:hover .projectImage {
  transform: scale(1.05);
}

.projectOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.projectCard:hover .projectOverlay {
  opacity: 1;
}

.viewButton {
  border-radius: 8px;
  font-weight: 500;
  font-size: 13px;
  padding: 8px 16px;
  background: #ffffff;
  color: #1e293b;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.viewButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #f8fafc;
}

/* 项目信息 - 简约设计 */
.projectInfo {
  padding: 16px;
  background: #ffffff;
}

.projectTitle {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.projectDescription {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-weight: 400;
}

/* 项目元信息 - 简约布局 */
.projectMeta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.authorInfo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.authorAvatar {
  flex-shrink: 0;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.authorName {
  font-size: 13px;
  color: #1e293b;
  font-weight: 500;
}

.classInfo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.classTag {
  margin: 0;
  font-size: 12px;
  border-radius: 6px;
  padding: 4px 8px;
  font-weight: 500;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  color: #64748b;
}

/* 项目底部 - 简约设计 */
.projectFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0 0 0;
  border-top: 1px solid #f1f5f9;
  margin-top: 8px;
}

.publishTime,
.viewCount {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #94a3b8;
  font-weight: 400;
}

/* 响应式设计 - 优化移动端体验 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .header {
    padding: 20px;
    margin-bottom: 20px;
  }

  .title {
    font-size: 24px;
    gap: 12px;
  }

  .titleIcon {
    padding: 10px;
  }

  .subtitle {
    font-size: 14px;
  }

  .controls {
    padding: 20px;
    margin-bottom: 20px;
  }

  .projectGrid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }

  .projectCover {
    height: 140px;
  }

  .projectInfo {
    padding: 14px;
  }

  .projectTitle {
    font-size: 15px;
  }

  .projectDescription {
    font-size: 13px;
  }

  .authorName {
    font-size: 14px;
  }

  .classTag {
    font-size: 12px;
    padding: 4px 8px;
  }
}

/* 平板端优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  .projectGrid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 14px;
    padding: 18px;
  }

  .projectCover {
    height: 150px;
  }

  .projectInfo {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 12px;
  }

  .header {
    padding: 16px;
    margin-bottom: 16px;
  }

  .title {
    font-size: 20px;
    gap: 10px;
  }

  .titleIcon {
    padding: 8px;
  }

  .subtitle {
    font-size: 13px;
  }

  .controls {
    padding: 16px;
    margin-bottom: 16px;
  }

  .projectGrid {
    padding: 16px;
    gap: 16px;
  }

  .projectCover {
    height: 120px;
  }

  .projectInfo {
    padding: 12px;
  }

  .projectTitle {
    font-size: 14px;
  }

  .projectDescription {
    font-size: 12px;
  }

  .authorInfo {
    padding: 8px 10px;
    gap: 8px;
  }

  .authorName {
    font-size: 13px;
  }

  .publishTime,
  .viewCount {
    font-size: 12px;
    padding: 6px 8px;
  }
}

/* 超大屏幕优化 */
@media (min-width: 1400px) {
  .projectGrid {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 32px;
    padding: 40px;
  }

  .projectCover {
    height: 240px;
  }

  .projectTitle {
    font-size: 20px;
  }

  .projectDescription {
    font-size: 16px;
  }
}

/* 动画效果增强 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.projectCard {
  animation: fadeInUp 0.6s ease-out;
}

.projectCard:nth-child(even) {
  animation-delay: 0.1s;
}

.projectCard:nth-child(3n) {
  animation-delay: 0.2s;
}
