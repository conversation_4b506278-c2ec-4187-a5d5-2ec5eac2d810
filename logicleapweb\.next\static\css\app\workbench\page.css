/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/workbench/components/SchoolSelectionModal.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
/* 学校选择弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  /* 移除性能消耗大的backdrop-filter */
  /* backdrop-filter: blur(12px); */
  /* 阻止背景滚动 */
  overflow: hidden;
  touch-action: none;
  /* 阻止滚轮事件传播到页面 */
  overscroll-behavior: contain;
  /* 启用硬件加速 */
  will-change: opacity;
  transform: translateZ(0);
}

.modal-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  width: 520px;
  max-width: 520px;
  min-width: 520px;
  /* 优化高度设置，避免遮挡底部内容 */
  height: auto;
  max-height: 75vh;
  min-height: 400px;
  overflow: hidden;
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 0 0 1px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  animation: modalSlideInOptimized 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  display: flex;
  flex-direction: column;
  /* 启用硬件加速 */
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* 优化后的动画，移除性能消耗大的blur滤镜 */
@keyframes modalSlideInOptimized {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1) translateZ(0);
  }
}

/* 保留原动画作为备用 */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.9);
    filter: blur(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

.modal-close-btn-outside {
  position: absolute;
  top: -25px;
  right: -38px;
  background: rgba(255, 255, 255, 0.95);
  border: none;
  cursor: pointer;
  padding: 12px;
  border-radius: 50%;
  color: #64748b;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.modal-close-btn-outside:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 12px 32px rgba(239, 68, 68, 0.25);
  border-color: rgba(239, 68, 68, 0.2);
}

.step-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 48px;
  gap: 80px;
  position: relative;
  background: linear-gradient(135deg, rgba(156, 186, 234, 0.5) 0%, rgba(156, 186, 234, 0.5) 100%);
  /* margin: 24px 24px 0 24px; */
  border-radius: 0px;
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

/* 删除123下面的蓝色连接线 */
/* .step-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 20%;
  right: 20%;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 50%, #93c5fd 100%);
  transform: translateY(-50%);
  z-index: 1;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
} */

/* 确保连接线不显示 */
.step-indicator::before {
  display: none !important;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: 3px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  color: #64748b;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.step.active .step-number {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #3b82f6;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.step-label {
  font-size: 13px;
  color: #64748b;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.step.active .step-label {
  color: #3b82f6;
  transform: translateY(-2px);
}

.modal-content-body {
  padding: 14px 48px 0px 48px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 0;
  /* 确保内容区域能够自适应高度 */
  min-height: 0;
}

/* 为有底部按钮的弹窗（如班级选择）添加底部padding */
.modal-content-body.has-footer {
  padding-bottom: 0px;
}

.section-title {
  font-size: 18px;
  color: #1e293b;
  margin-bottom: 32px;
  font-weight: 600;
  text-align: center;
  position: relative;
  flex-shrink: 0;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
  border-radius: 2px;
}

.schools-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  /* 使用flex-grow让容器自适应高度 */
  flex: 1;
  max-height: calc(75vh - 200px);
  padding: 20px 20px 30px 20px;
  margin-bottom: 0px;
}

.schools-grid::-webkit-scrollbar {
  width: 10px;
}

.schools-grid::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.08);
  border-radius: 5px;
  margin: 10px 0;
}

.schools-grid::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.4);
  border-radius: 5px;
  transition: background 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.schools-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.6);
  border-color: rgba(59, 130, 246, 0.2);
}

.school-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 2px solid transparent;
  border-radius: 16px;
  padding: 20px 16px;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: visible;
  /* 移除性能消耗大的backdrop-filter */
  /* backdrop-filter: blur(10px); */
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
  z-index: 1;
  /* 启用硬件加速 */
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

.school-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  border-radius: 18px;
}

.school-card:hover::before {
  opacity: 0.05;
}

.school-card:hover {
  box-shadow: 0 25px 80px rgba(59, 130, 246, 0.25);
  border-color: #3b82f6;
  z-index: 10;
}

.school-card-name {
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.3px;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  text-align: center;
  display: block;
  width: 100%;
  margin-bottom: 8px;
}



.school-card-location {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: #64748B;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  margin-top: 4px;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.school-card:hover .school-card-location {
  opacity: 1;
  color: #475569;
}











.modal-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  /* 减少底部按钮区域的padding */
  padding: 16px 24px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0 0 24px 24px;
  flex-shrink: 0;
  z-index: 10;
}

/* 使用与模板选择模态框一致的底部按钮样式 */
.modal-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 20px 24px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 50%, #e6f3ff 100%);
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 0 0 24px 24px;
  flex-shrink: 0;
  gap: 12px;
}

.prev-btn {
  flex: 1;
  max-width: 120px;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  color: #3b82f6;
  border-color: #3b82f6;
  backdrop-filter: blur(10px);
}

.prev-btn:hover {
  background: #3b82f6;
  color: white;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* 移除浮动效果后不再需要额外的间距调整 */

/* 确保滚动容器不会裁剪浮动元素 - 已在主样式中设置 */

/* 优化滚动条在浮动时的表现 */
.schools-grid::-webkit-scrollbar {
  z-index: 999;
}

/* 防止背景元素干扰 */
.modal-content-body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-content {
    min-width: 320px;
    max-width: 95%;
    height: 90vh;
    min-height: 400px;
    margin: 10px;
  }



  .step-indicator {
    padding: 24px 32px;
    gap: 40px;
    margin: 0 16px;
  }

  /* 删除响应式蓝色连接线 */
  /* .step-indicator::before {
    left: 25%;
    right: 25%;
  } */

  .step-number {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .step-label {
    font-size: 11px;
  }

  .modal-content-body {
    padding: 20px 24px 0px 24px;
  }

  .modal-content-body.has-footer {
    padding-bottom: 0px;
  }

  .schools-grid {
    padding: 16px 16px 24px 16px;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 24px;
  }

  .school-card {
    padding: 18px 12px;

  }

  .school-card-name {
    font-size: 18px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 98%;
    min-width: 280px;
    height: 85vh;
    min-height: 350px;
  }

  .step-indicator {
    padding: 20px 16px;
    gap: 20px;
    margin: 0 12px;
  }

  .step-number {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .step-label {
    font-size: 10px;
  }

  .modal-content-body {
    padding: 16px 20px 0px 20px;
  }

  .modal-content-body.has-footer {
    padding-bottom: 0px;
  }

  .schools-grid {
    padding: 12px 12px 80px 12px;
  }

  .section-title {
    font-size: 14px;
    margin-bottom: 20px;
  }

  .school-card {
    padding: 16px 10px;
    text-align: center;
  }

  .school-card-name {
    font-size: 16px;
  }

  .modal-footer {
    padding: 16px 20px;
  }

  .prev-btn {
    padding: 10px 20px;
    font-size: 14px;
    max-width: 100px;
  }

  .modal-close-btn-outside {
    top: -15px;
    right: -15px;
    width: 36px;
    height: 36px;
    padding: 8px;
  }
}

@media (min-height: 800px) {
  .modal-content {
    height: 650px;
  }
}

@media (max-height: 600px) {
  .modal-content {
    height: 90vh;
    min-height: 400px;
  }

  .step-indicator {
    padding: 20px 32px;
  }

  .modal-content-body {
    padding: 16px 32px 0px 32px;
  }

  .modal-content-body.has-footer {
    padding-bottom: 0px;
  }

  .section-title {
    margin-bottom: 20px;
  }
}





/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/workbench/components/TemplatePickerModal.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/* 模板选择弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999; /* 确保在最顶层 */
  overflow: hidden;
  touch-action: none;
  overscroll-behavior: contain;
  will-change: opacity;
  transform: translateZ(0);
}

.template-picker-modal {
  max-width: 520px;
  min-width: 420px;
  height: 660px;
  background: #ffffff;
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06), 0 4px 16px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.template-picker-modal .modal-header {
  padding: 20px 60px 0 24px; /* 右边留出关闭按钮的空间 */
  display: flex;
  justify-content: flex-start; /* 返回按钮靠左 */
  align-items: center;
  background: #fafbfc;
  position: relative;
  min-height: 60px; /* 确保有足够的高度 */
}

/* 关闭按钮始终在右边 */
.template-picker-modal .modal-header .close-btn {
  position: absolute;
  right: 24px;
  top: 50%;
  transform: translateY(-50%); /* 垂直居中 */
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  color: #6b7280;
  transition: all 0.2s ease;
  z-index: 10;
}

.template-picker-modal .modal-header .close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 14px;
  background: #f9fafb;
  border: 1px solid #f0f1f3;
  border-radius: 8px;
  color: #6b7280;
  font-size: 13px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #f3f4f6;
  border-color: #e5e7eb;
  color: #4b5563;
}

/* 标签页样式 */
.template-tabs {
  display: flex;
  margin: 0 24px 24px 24px;
  background: #f9fafb;
  border-radius: 12px;
  padding: 3px;
  position: relative;
  border: 1px solid #f0f1f3;
}

.tab-button {
  flex: 1;
  padding: 12px 18px;
  background: transparent !important;
  background-color: transparent !important;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  box-shadow: none !important;
  border-radius: 8px;
  text-align: center;
}

.tab-button.active {
  color: #6b7280;
  background: white !important;
  background-color: white !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid #f0f1f3;
}

.tab-button.active::after {
  display: none;
}

.tab-button:hover:not(.active) {
  color: #6b7280 !important;
  background: rgba(0, 0, 0, 0.02) !important;
  background-color: rgba(0, 0, 0, 0.02) !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* 强制移除所有可能的背景效果 */
.tab-button:focus,
.tab-button:active {
  outline: none !important;
}

/* 特别针对模板选择按钮的悬停效果 */
.template-tabs .tab-button:hover:not(.active) {
  background: rgba(0, 0, 0, 0.02) !important;
  background-color: rgba(0, 0, 0, 0.02) !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
}

/* 模板网格样式 */
.template-picker-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0 24px 24px 24px;
  max-height: 540px;
  overflow-y: auto;
}

/* 模板卡片样式 - 现代化设计 */
.template-picker-card {
  background: white;
  border: 1px solid #f0f1f3;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  padding: 0;
  min-height: 96px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.template-picker-card:hover {
  border-color: #e5e7eb;
  background-color: #fafbfc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.template-card-content {
  display: flex;
  align-items: center;
  padding: 18px 20px;
  gap: 16px;
  width: 100%;
  min-height: 96px;
  overflow: hidden;
}

/* 模板图标区域 */
.template-card-icon {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 20px;
  box-shadow: none;
}

/* 官方模板图标 - 淡黄色背景 */
.template-card-icon.official-template {
  background: #fef3c7;
  color: #d97706;
}

/* 特殊模板图标 - 深黄色背景 */
.template-card-icon.special-template {
  background: #fbbf24;
  color: #ffffff;
}

/* 我的模板图标 - 淡蓝色背景 */
.template-card-icon.my-template {
  background: #dbeafe;
  color: #2563eb;
}

/* 模板信息区域 */
.template-card-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px; /* 减小间距 */
  justify-content: center;
}

.template-card-title {
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.4;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.template-card-subtitle {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.template-card-description {
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
  margin: 0;
  /* 单行文字省略，避免过长描述 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}



/* 模板标签 */
.template-card-tags {
  display: flex;
  gap: 6px; /* 减小标签间距 */
  margin-top: 2px; /* 减小上边距 */
  margin-bottom: 2px; /* 增加下边距，避免底部拥挤 */
  flex-wrap: wrap;
  align-items: center;
}

.template-tag {
  display: inline-block;
  padding: 2px 6px;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
}

.template-tag.official {
  background: #fef3c7;
  color: #d97706;
}

.template-tag.my {
  background: #dbeafe;
  color: #2563eb;
}

/* 操作按钮区域 */
.template-card-action {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.template-use-btn {
  padding: 6px 12px;
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.template-use-btn:hover {
  background: #e5e7eb;
  color: #4b5563;
}

/* 官方模板按钮样式 */
.template-use-btn.official-template-btn {
  background: #fef3c7;
  color: #d97706;
  border: 1px solid #fde68a;
}

.template-use-btn.official-template-btn:hover {
  background: #fde68a;
  color: #b45309;
}

/* 我的模板按钮样式 */
.template-use-btn.my-template-btn {
  background: #dbeafe;
  color: #2563eb;
  border: 1px solid #bfdbfe;
}

.template-use-btn.my-template-btn:hover {
  background: #bfdbfe;
  color: #1d4ed8;
}

/* 加载和空状态样式 */
.template-picker-grid .loading-placeholder,
.template-picker-grid .empty-placeholder {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  border: 2px dashed #e2e8f0;
}

.template-picker-grid .loading-placeholder .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #E5E7EB;
  border-top: 3px solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.template-picker-grid .loading-placeholder p,
.template-picker-grid .empty-placeholder p {
  color: #6B7280;
  font-size: 14px;
  margin: 0;
}

/* 滚动条样式 */
.template-picker-grid::-webkit-scrollbar {
  width: 6px;
}

.template-picker-grid::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.template-picker-grid::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.template-picker-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-picker-modal {
    min-width: 320px;
    max-width: 95%;
    height: 80vh;
  }

  .template-picker-grid {
    gap: 12px;
    padding: 0 15px 15px 15px;
  }

  .template-card-content {
    padding: 14px;
    gap: 12px;
    min-height: 90px;
  }

  .template-card-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .template-card-title {
    font-size: 13px;
  }

  .template-card-subtitle,
  .template-card-description {
    font-size: 11px;
  }

  .template-use-btn {
    padding: 6px 12px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .template-picker-grid {
    gap: 10px;
    padding: 0 10px 10px 10px;
  }

  .template-card-content {
    padding: 12px;
    gap: 12px;
    flex-direction: column;
    text-align: center;
    min-height: 120px;
  }

  .template-card-icon {
    width: 36px;
    height: 36px;
    font-size: 18px;
    align-self: center;
  }

  .template-card-info {
    text-align: center;
  }

  .template-card-title {
    font-size: 12px;
  }

  .template-card-description {
    font-size: 10px;
  }

  .template-card-tags {
    justify-content: center;
  }

  .template-use-btn {
    padding: 6px 10px;
    font-size: 10px;
  }

  /* 移动端优化 */
  .modal-content-body {
    height: calc(100vh - 150px);
    max-height: none;
  }

  .students-grid {
    grid-template-columns: 1fr;
    max-height: 200px;
  }

  .student-card {
    padding: 10px;
  }

  .student-selection-area {
    padding: 16px;
    margin-top: 16px;
  }
}

/* 模态框内容布局 */
.modal-content-body {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200px);
  max-height: 600px;
  min-height: 400px;
}

.modal-content-header {
  flex-shrink: 0;
  /* padding: 0 0 16px 0; */
}

.modal-content-scrollable {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  padding-top: 10px;
  margin-right: -5px;
  margin-left: -5px;
}

.modal-content-scrollable::-webkit-scrollbar {
  width: 6px;
}

.modal-content-scrollable::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.modal-content-scrollable::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.modal-content-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

.modal-footer {
  flex-shrink: 0;
  /* margin-top: 16px; */
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  /* justify-content: space-between; */
  align-items: center;
}

/* 学生选择区域样式 */
.student-selection-area {
  margin-top: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.student-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.student-selection-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.student-selection-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selection-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.select-all-btn {
  padding: 6px 12px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.select-all-btn:hover {
  background: #2563eb;
}

.selected-count {
  font-size: 12px;
  color: #6b7280;
}

.min-points {
  font-size: 11px;
  color: #059669;
  font-weight: 500;
}

.students-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  max-height: 250px;
  overflow-y: auto;
  padding-right: 8px;
  margin-right: -8px;
}

.students-grid::-webkit-scrollbar {
  width: 6px;
}

.students-grid::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.students-grid::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.students-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

.student-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.student-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.student-card.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.student-card.insufficient {
  border-color: #ef4444;
  background: #fef2f2;
}

.student-card.insufficient:hover {
  border-color: #dc2626;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
}

.student-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.student-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #6b7280;
  font-size: 14px;
}

.student-info {
  flex: 1;
  min-width: 0;
}

.student-name {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.student-number {
  font-size: 12px;
  color: #6b7280;
}

.student-points {
  margin-top: 4px;
}

.points-value {
  font-size: 11px;
  color: #059669;
  font-weight: 500;
}

.points-value.insufficient {
  color: #ef4444;
}

.loading-text {
  font-size: 11px;
  color: #9ca3af;
}

.selected-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
}

.insufficient-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 12px;
  color: #ef4444;
  background: #fef2f2;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #fecaca;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6b7280;
}

.no-students {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #9ca3af;
  text-align: center;
}

.no-students p {
  margin: 12px 0 0 0;
  font-size: 14px;
}

.assign-energy-section {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.assign-energy-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.assign-energy-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.assign-energy-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 加载动画 */
.loading-spinner-small {
  width: 12px;
  height: 12px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 发布任务相关样式 */
.publish-task-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 标签页切换器 */
.tab-switcher {
  display: flex;
  justify-content: center;
  /* margin: 20px 0; */
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 4px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.tab-btn {
  flex: 1;
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: #64748b;
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab-btn.active {
  /* background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); */
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 任务信息表单样式 - 紧凑简洁设计 */
.task-info-tab {
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* 紧凑输入框样式 */
.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
  color: #1f2937;
  background: #ffffff;
  transition: all 0.2s ease;
  outline: none;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.form-input:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background: #ffffff;
}

.form-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

/* 紧凑文本域样式 */
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
  color: #1f2937;
  background: #ffffff;
  transition: all 0.2s ease;
  outline: none;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.form-textarea:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.form-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background: #ffffff;
}

.form-textarea::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

/* 标签样式 */
.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
  letter-spacing: 0.025em;
}

/* 自评项样式 - 紧凑设计 */
.self-assessment-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 添加自评项按钮 - 紧凑虚线边框设计 */
.add-self-assessment-btn {
  width: 100%;
  padding: 12px 16px;
  background: #fafbfc;
  color: #6b7280;
  border: 1px dashed #d1d5db;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  position: relative;
  overflow: hidden;
}

.add-self-assessment-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.add-self-assessment-btn:hover {
  background: #f8fafc;
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.add-self-assessment-btn:hover::before {
  left: 100%;
}

/* 自评项容器 - 紧凑设计 */
.self-assessment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 2px 0;
  position: relative;
}

.self-assessment-item .form-input {
  flex: 1;
  margin-bottom: 0;
}

/* 删除按钮 - 紧凑圆形设计 */
.remove-btn {
  width: 32px;
  height: 32px;
  background: #fee2e2;
  color: #ef4444;
  border: 1px solid #fecaca;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  line-height: 1;
  transition: all 0.2s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.remove-btn:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* 添加更多自评项按钮 - 紧凑设计 */
.add-btn {
  padding: 8px 12px;
  background: #f0f9ff;
  color: #3b82f6;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-top: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.add-btn:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

/* 任务持续时间设置样式 - 选项按钮设计 */
.time-settings {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 持续时间选项容器 */
.duration-options {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
  margin: 8px 0;
}

/* 持续时间选项按钮 */
.duration-option {
  padding: 10px 12px;
  background: #ffffff;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-align: center;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.duration-option:hover {
  border-color: #3b82f6;
  background: #f0f9ff;
  color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.duration-option.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.duration-option.active:hover {
  background: #2563eb;
  border-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(59, 130, 246, 0.3);
}

/* 持续时间提示文字 */
.duration-hint {
  font-size: 12px;
  color: #6b7280;
  margin: 4px 0 0 0;
  line-height: 1.4;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* 资源标签页样式 - 紧密布局 */
.resources-tab {
  padding: 12px 0px 80px 0px;
  display: flex;
  flex-direction: column;
  gap: 18px; /* 减少间距 */
}

.works-section,
.attachments-section {
  display: flex;
  flex-direction: column;
  gap: 12px; /* 内部元素间距 */
}

.works-section h4,
.attachments-section h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-text {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* 作品滚动包装器 - 去掉外框，参考班级空间设计 */
.works-scroll-wrapper {
  position: relative;
  width: 100%;
  touch-action: pan-x;
  overscroll-behavior: contain;
  min-height: 200px;
}

/* 水平滚动作品容器 - 去掉外框，参考班级空间设计 */
.works-horizontal-scroll {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 8px 0;
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
  position: relative;
  cursor: grab;
  touch-action: pan-x;
  overscroll-behavior: contain;
  min-height: 200px;
}

.works-horizontal-scroll:active {
  cursor: grabbing;
}

.works-horizontal-scroll::-webkit-scrollbar {
  height: 6px;
}

.works-horizontal-scroll::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.works-horizontal-scroll::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.works-horizontal-scroll::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px; /* 减少高度 */
  color: #9ca3af;
  font-size: 14px;
  font-weight: 500;
  gap: 8px;
}

.empty-placeholder::before {
  content: "📁";
  font-size: 24px;
  opacity: 0.6;
}

.works-horizontal-container {
  display: flex;
  gap: 16px;
  min-width: max-content;
  /* 确保容器内的元素也能响应鼠标事件 */
  pointer-events: auto;
  /* 添加最小高度确保有足够的滚动区域 */
  min-height: 200px;
}

/* 加载和空状态容器样式 */
.loading-container,
.empty-works {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  /* 确保能够捕获滚轮事件 */
  pointer-events: auto;
  width: 100%;
}

/* 作品卡片样式 - 参考班级空间设计，增大尺寸 */
.work-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  width: 200px;
}

.work-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 8px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.work-card.selected {
  border-color: #3b82f6;
  background: #fefefe;
  box-shadow: 0 4px 12px -2px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

/* 作品预览区域 - 参考班级空间设计 */
.work-preview {
  aspect-ratio: 16/10;
  background: #f9fafb;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.work-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.work-card:hover .work-image {
  transform: scale(1.02);
}

.work-placeholder {
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 选择指示器 - 参考班级空间设计 */
.selection-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e5e7eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: transparent;
}

.selection-indicator.selected {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

/* 作品信息区域 */
.work-info {
  padding: 0;
}

.work-placeholder {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 12px;
  border-radius: 6px;
}

/* 作品信息 */
.work-info {
  position: relative;
  height: 40px; /* 固定高度保持作品框大小一致 */
}

.work-header {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  height: 100%;
}

/* 作品标题 - 参考班级空间设计 */
.work-title {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 6px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}



/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.empty-works {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #9ca3af;
  text-align: center;
}

/* 加载更多遮罩 */
.loading-more-overlay {
  position: absolute;
  inset: 0;
  backdrop-filter: blur(4px);
  background: rgba(255, 255, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 50;
  border-radius: 8px;
  animation: fadeIn 0.3s ease-in-out;
}

.loading-more-content {
  background: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  border: 1px solid #e5e7eb;
}

.loading-more-content p {
  margin: 0;
  color: #374151;
  font-weight: 500;
}

.loading-more-subtitle {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.loading-spinner-small {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 附件上传部分 */
.attachments-section {
  display: flex;
  flex-direction: column;
  gap: 12px; /* 统一间距 */
}

.upload-area {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 12px;
  background: #f8fafc;
  border: 1px dashed #cbd5e1;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.upload-area:hover {
  background: #f1f5f9;
  border-color: #94a3b8;
}

.upload-btn {
  width: 36px;
  height: 36px;
  border: 2px dashed #94a3b8;
  border-radius: 6px;
  background: white;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 18px;
  font-weight: 300;
  line-height: 1;
  flex-shrink: 0;
}

.upload-btn:hover {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
  transform: scale(1.05);
}

.file-format-info {
  font-size: 11px;
  color: #64748b;
  line-height: 1.4;
  flex: 1;
}

/* 加载更多按钮容器 */
.load-more-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 120px;
  height: 100%;
  min-height: 140px;
}

.load-more-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100px;
  height: 100px;
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  font-weight: 500;
}

.load-more-btn:hover:not(:disabled) {
  background: #f1f5f9;
  border-color: #94a3b8;
  color: #475569;
}

.load-more-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  max-height: 120px; /* 限制高度 */
  overflow-y: auto;
  padding: 2px; /* 防止阴影被裁切 */
}

.attachment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.attachment-item:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-name {
  font-size: 12px;
  color: #374151;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-attachment-btn {
  width: 20px;
  height: 20px;
  border: none;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.remove-attachment-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.loading {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

/* 详细官方模板页面样式 */
.template-detail-header {
  padding: 0 20px 16px 20px;
  border-bottom: 1px solid #e2e8f0;
}

.template-detail-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.template-detail-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  max-height: 450px;
  overflow-y: auto;
}

.template-detail-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.template-detail-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.template-detail-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 16px;
}

.template-detail-icon {
  width: 48px;
  height: 48px;
  background: #fef3c7;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.template-detail-info {
  flex: 1;
  min-width: 0;
}

.template-detail-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
  line-height: 1.4;
}

.template-detail-tags {
  margin-bottom: 6px;
}

.tag-official {
  display: inline-block;
  padding: 2px 8px;
  background: #fbbf24;
  color: white;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

.template-detail-description {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
  line-height: 1.3;
}

.template-detail-usage {
  font-size: 11px;
  color: #64748b;
}

.template-detail-action {
  flex-shrink: 0;
}

.use-template-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.use-template-btn:hover {
  background: #2563eb;
}

/* 返回按钮样式 */
.back-btn {
  padding: 8px 16px;
  background: transparent;
  color: #3b82f6;
  border: 1px solid #3b82f6;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  margin-right: auto;
}

.back-btn:hover {
  background: #3b82f6;
  color: white;
}



/* 详细页面滚动条样式 */
.template-detail-grid::-webkit-scrollbar {
  width: 6px;
}

.template-detail-grid::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.template-detail-grid::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.template-detail-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/workbench/components/NewPublishTaskModal.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/* 新发布任务模态框样式 */
.new-publish-task-modal {
  max-width: 520px;
  min-width: 520px;
  height: 700px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 50%, #e6f3ff 100%);
}

/* 新发布任务弹窗使用外部关闭按钮样式 */
.modal-close-btn-outside {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(229, 231, 235, 0.6);
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  color: #9ca3af;
  transition: all 0.2s ease;
  z-index: 1001;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
}

.modal-close-btn-outside:hover {
  background: rgba(255, 255, 255, 1);
  color: #374151;
  border-color: rgba(209, 213, 219, 0.8);
  transform: scale(1.05);
}

.new-publish-task-modal .modal-content-body {
  padding: 0px 40px 40px 40px;
  flex: 1;
  overflow-y: auto;
}

/* 标签页切换器 */
.tab-switcher {
  display: flex;
  justify-content: center;
  margin: 20px 0px 0px 0px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50px;
  padding: 4px;
  margin-left: 40px;
  margin-right: 40px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.tab-btn {
  flex: 1;
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: #64748b;
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab-btn.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.tab-btn:hover:not(.active) {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

/* 任务信息标签页 */
.task-info-tab {
  display: flex;
  flex-direction: column;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  outline: none;
}

.form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: white;
}

.form-input::placeholder {
  color: #9ca3af;
}

.form-textarea {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  outline: none;
  resize: vertical;
  min-height: 55px; /* 从80px减小到55px */
  font-family: inherit;
}

.form-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: white;
}

.form-textarea::placeholder {
  color: #9ca3af;
}

/* 自评项部分 */
.self-assessment-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.self-assessment-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.self-assessment-item .form-input {
  flex: 1;
}

.remove-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.add-btn {
  padding: 12px 20px;
  border: 2px dashed #3b82f6;
  background: transparent;
  color: #3b82f6;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: #1d4ed8;
  color: #1d4ed8;
}

/* 资源与附件标签页 */
.resources-tab {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding:20px 0px 80px 0px
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
}

/* 作品选择部分 */
.works-section {
  display: flex;
  flex-direction: column;
}

.works-section h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.help-text {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 16px;
}

/* 作品滚动包装器 */
.works-scroll-wrapper {
  position: relative;
  width: 100%;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid #e2e8f0;
  backdrop-filter: blur(10px);
  touch-action: pan-x;
  overscroll-behavior: contain;
}

.works-scroll-wrapper:hover {
  background-color: rgba(59, 130, 246, 0.02);
}

/* 水平滚动作品容器 */
.works-horizontal-scroll {
  overflow-x: auto;
  overflow-y: hidden;
  padding: 20px;
  padding-bottom: 16px;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
  display: flex;
  gap: 16px;
  min-height: 180px;
  overscroll-behavior-x: contain;
  overscroll-behavior-y: none;
}

.works-horizontal-scroll::-webkit-scrollbar {
  height: 6px;
}

.works-horizontal-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.works-horizontal-scroll::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.works-horizontal-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #6b7280;
  font-size: 14px;
  height: 160px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.empty-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 160px;
  color: #9ca3af;
  font-size: 16px;
  font-weight: 500;
}

.work-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  text-align: center;
  display: flex;
  flex-direction: column;
  min-width: 130px;
  max-width: 130px;
  height: 130px;
  flex-shrink: 0;
  gap: 8px;
}

.work-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.work-card.selected {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.work-image {
  width: 100%;
  height: 70px;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}

.work-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.work-placeholder {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 12px;
}

.work-title {
  font-size: 11px;
  color: #1f2937;
  font-weight: 600;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 6px;
  text-align: center;
  padding: 0 2px;
  flex-shrink: 0;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* 附件上传部分 */
.attachments-section {
  display: flex;
  flex-direction: column;
}

.attachments-section h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.upload-area {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.upload-btn {
  width: 60px;
  height: 60px;
  border: 2px dashed #3b82f6;
  border-radius: 8px;
  background: transparent;
  color: #3b82f6;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 24px;
  font-weight: 300;
  line-height: 1;
}

.upload-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: #1d4ed8;
  color: #1d4ed8;
}

.file-format-info {
  font-size: 12px;
  color: #6b7280;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.file-name {
  font-size: 14px;
  color: #374151;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-attachment-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.remove-attachment-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* 底部按钮 */
.new-publish-task-modal .modal-footer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  /* padding: 20px 40px 40px 40px; */
  gap: 16px;
}

.start-class-btn {
  padding: 16px 48px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: not-allowed;
  transition: all 0.3s ease;
  min-width: 160px;
  margin-left: auto;
  /* 默认为禁用状态样式 */
  background: #f3f4f6;
  color: #d1d5db;
  box-shadow: none;
  opacity: 0.6;
  border: 1px solid #e5e7eb;
}

.start-class-btn.enabled {
  background-color: #3b82f6;
  color: white;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
  cursor: pointer;
  opacity: 1;
  border: none;
}

.start-class-btn.enabled:hover {
  background-color: #2563eb;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.start-class-btn.disabled {
  background: #f3f4f6;
  color: #d1d5db;
  cursor: not-allowed;
  box-shadow: none;
  opacity: 0.6;
  border: 1px solid #e5e7eb;
}

.start-class-btn.disabled:hover {
  background: #f3f4f6;
  color: #d1d5db;
  cursor: not-allowed;
  box-shadow: none;
  opacity: 0.6;
  transform: none;
}

/* 上一步按钮样式 */
.prev-btn {
  padding: 16px 32px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  color: #64748b;
  min-width: 120px;
}

.prev-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-1px);
}

/* 步骤指示器已完成状态样式 */
.new-publish-task-modal .step.completed .step-number {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.new-publish-task-modal .step.completed .step-label {
  color: #1e293b;
  font-weight: 600;
}

/* 动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .new-publish-task-modal {
    min-width: 320px;
    max-width: 95%;
    height: 90vh;
  }

  .new-publish-task-modal .modal-content-body {
    padding: 20px 24px 32px 24px;
  }

  .tab-switcher {
    margin-left: 24px;
    margin-right: 24px;
  }

  .works-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
  }

  .new-publish-task-modal .modal-footer {
    padding: 0 24px 32px 24px;
  }

  /* 移动端分页器调整 */
  .works-content {
    gap: 12px; /* 移动端减少间距 */
  }

  .pagination-btn {
    width: 44px;
    height: 44px;
  }

  .pagination-btn svg {
    width: 20px;
    height: 20px;
  }

  .pagination-info {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }

  .works-grid-wrapper {
    min-height: 150px;
  }
}

/* 针对新HTML结构的作品卡片样式 - 信息与图片在同一容器 */
.work-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: #f3f4f6;
}

.work-image {
  position: relative;
  /* width: 100%;
  height: 100%;  */
  border-radius: 8px;
  overflow: hidden;
  background: #f3f4f6;
}

.work-cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.work-image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
  padding: 8px 12px;
  font-size: 12px;
}

.work-image-label {
  font-size: 12px;
  color: white;
  font-weight: 500;
}

.work-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  backdrop-filter: blur(4px);
  padding: 12px;
  border-radius: 0 0 8px 8px;
}

.work-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.work-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 8px;
}

.work-status {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 12px;
  flex-shrink: 0;
  font-weight: 500;
  backdrop-filter: blur(4px);
}

.work-status.unpublished {
  background: rgba(254, 243, 199, 0.9);
  color: #d97706;
}

.work-status.published {
  background: rgba(220, 252, 231, 0.9);
  color: #16a34a;
}

.work-description {
  font-size: 12px;
  color: #6b7280;
  margin: 0 0 4px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.work-time {
  font-size: 11px;
  color: #9ca3af;
  margin-bottom: 0;
  line-height: 1.3;
}

.work-checkbox {
  position: absolute;
  bottom: 8px;
  right: 8px;
  z-index: 10;
}

.work-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #3b82f6;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 分页器样式 - 外侧布局 */
.works-content {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 16px;
}

.works-grid-wrapper {
  flex: 1;
  min-height: 200px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  background: #ff6b35;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  backdrop-filter: blur(8px);
  flex-shrink: 0;
}

/* 左右按钮在flex布局中无需特殊定位 */

.pagination-btn:hover:not(.disabled) {
  background: #e55a2b;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(229, 90, 43, 0.4);
}

.pagination-btn:active:not(.disabled) {
  transform: scale(1.05);
}

.pagination-btn.disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: #ffb3a0;
  color: rgba(255, 255, 255, 0.7);
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.2);
  transform: scale(0.9);
}

.pagination-btn svg {
  width: 24px;
  height: 24px;
  transition: transform 0.2s ease;
}

.next-btn:hover:not(.disabled) svg {
  transform: translateX(3px);
}

.prev-btn:hover:not(.disabled) svg {
  transform: translateX(-3px);
}

/* 按钮出现动画 */
.pagination-btn {
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 当没有作品时隐藏按钮 */
.works-content:has(.empty-placeholder) .pagination-btn {
  display: none;
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8fafc;
  border-radius: 8px;
  font-size: 12px;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.page-info {
  font-weight: 500;
  color: #374151;
}

.total-info {
  color: #6b7280;
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/workbench/components/TemplateSelectionModal.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
/* 模板选择弹窗样式 */
.template-selection-modal {
  width: 520px; /* 固定宽度，防止内容撑开 */
  max-width: 520px;
  min-width: 520px;
  height: 700px;
  background: #ffffff;
  touch-action: manipulation; /* 允许基本的触摸操作，但禁用双击缩放等 */
  overscroll-behavior: contain; /* 阻止滚动链传播 */
}

/* 模板选择弹窗使用外部关闭按钮样式 */

.template-selection-modal .modal-content-body {
  padding: 0px 40px 80px 40px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden; /* 防止水平滚动 */
}

.template-selection-modal .section-title {
  font-size: 18px;
  color: #1e293b;
  /* margin-bottom: 32px; */
  font-weight: 600;
  /* text-align: center; */
  position: relative;
  flex-shrink: 0;
  margin-top: 10px;
}

.template-selection-modal .section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: #3b82f6;
  border-radius: 1px;
}

/* 分配选项样式 */
.distribution-options {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  justify-content: center;
  align-items: flex-start;
}

.distribution-card {
  flex: 1;
  max-width: 140px;
  min-width: 100px;
  min-height: 65px;
  background: #ffffff;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  padding: 12px 8px;
}

.distribution-card:hover {
  border-color: #cbd5e1;
  background: #f8fafc;
}

.distribution-card.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

/* 禁用状态样式 */
.distribution-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
  border-color: #e5e7eb !important;
  background: #f9fafb !important;
}

.distribution-card.disabled .distribution-label {
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: unset !important;
  background-clip: unset !important;
  color: #6b7280 !important;
}

/* 能量输入框样式 */
.energy-input-container {
  margin-top: 8px;
  width: 100%;
  animation: slideDown 0.3s ease-out;
}

.energy-input {
  width: 100%;
  height: 28px;
  border: 1px solid #3b82f6;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  color: #1e293b;
  outline: none;
  transition: all 0.3s ease;
}

.energy-input:focus {
  border-color: #1d4ed8;
  background: white;
}

.energy-input::placeholder {
  color: #94a3b8;
  font-size: 11px;
}

/* 当前可分配能量样式 */
.available-energy {
  background: #eff6ff;
  border: 1px solid #93c5fd;
  border-radius: 8px;
  padding: 12px 16px;
  margin-top: 16px;
  margin-bottom: 16px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #3b82f6;
}

/* 能量信息提示样式 - 全新设计 */
.min-available-energy {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 16px;
  margin-top: 12px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #475569;
  transition: all 0.2s ease;
}

.min-available-energy:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

/* 加载文字样式 */
.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #6b7280;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 动画效果 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.distribution-label {
  font-size: 14px;
  font-weight: 600;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.3px;
  text-align: center;
  transition: all 0.3s ease;
  line-height: 1.2;
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.distribution-card.selected .distribution-label {
  background: linear-gradient(135deg, #1d4ed8 0%, #0f172a 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 模板选择区域样式 */
.template-selection-area {
  margin-bottom: 20px;
}

/* 模板选择选项样式 */
.template-options {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.template-option {
  flex: 1;
  background: #ffffff;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;
}

.template-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.template-option.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.option-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.option-desc {
  font-size: 12px;
  color: #64748b;
}

/* 已选择模板样式 - 与内层风格统一 */
.template-selected {
  background: white;
  border: 1px solid #f0f1f3;
  border-radius: 12px;
  padding: 18px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
  transition: all 0.2s ease;
}

.template-selected:hover {
  border-color: #e5e7eb;
  background-color: #fafbfc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 模板图标 - 与内层卡片风格一致 */
.template-icon {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  background: #fef3c7;
  color: #d97706;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 20px;
  box-shadow: none;
}

.template-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
  justify-content: center;
}

.template-name {
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.4;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.template-label {
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.change-template-btn {
  padding: 6px 12px;
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.change-template-btn:hover {
  background: #e5e7eb;
  color: #4b5563;
}

.cancel-template-btn {
  background: #ef4444;
  border: none;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.cancel-template-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* 旧的模板占位符样式已移除，使用新的模板选择设计 */

/* 旧的取消按钮样式已移除，使用新设计中的样式 */

/* 底部按钮样式 */
.template-selection-modal .modal-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 20px 24px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 50%, #e6f3ff 100%);
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 0 0 24px 24px;
  flex-shrink: 0;
  gap: 12px;
}

.prev-btn, .next-btn {
  flex: 1;
  max-width: 120px;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* 发布中状态时保持按钮大小一致 */
.next-btn.publishing {
  max-width: 120px;
  min-width: 120px;
}

.prev-btn {
  background: #ffffff;
  color: #3b82f6;
  border-color: #3b82f6;
}

.prev-btn:hover {
  background: #3b82f6;
  color: white;
}

.next-btn.enabled {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.next-btn.enabled:hover {
  background: #1d4ed8;
}

.next-btn.disabled {
  background: #f9fafb;
  color: #9ca3af;
  border-color: #e5e7eb;
  cursor: not-allowed;
}

/* 发布中状态样式 */
.publishing-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  font-size: 15px;
  font-weight: 600;
  white-space: nowrap;
  letter-spacing: 0.3px;
}

.spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  flex-shrink: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 步骤指示器样式覆盖 */
.template-selection-modal .step.completed .step-number {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.template-selection-modal .step.completed .step-label {
  color: #1e293b;
  font-weight: 600;
}

/* 步骤指示器连接线已删除 */
/* 确保连接线不显示 */
.template-selection-modal .step-indicator::before {
  display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-selection-modal {
    min-width: 320px;
    max-width: 95%;
    height: 90vh;
  }

  .template-selection-modal .modal-content-body {
    padding: 20px 24px 80px 24px;
  }

  .distribution-options {
    flex-direction: column;
    gap: 12px;
  }

  .distribution-card {
    max-width: none;
    min-height: 55px;
  }

  .template-placeholder {
    height: 120px;
    font-size: 14px;
  }

  .template-selection-modal .modal-footer {
    flex-direction: column;
    gap: 12px;
    padding: 20px 16px;
  }

  .prev-btn, .next-btn {
    max-width: none;
  }

  /* 响应式连接线样式已删除 */
}

/* 时间设置样式 */
/* .time-settings {
  margin-top: 24px;
} */

.time-row {
  display: flex;
  gap: 16px;
  align-items: flex-end;
}

.time-field-container {
  flex: 1;
  position: relative;
}

.time-field {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 快捷时间选择器样式 */
.quick-time-selector {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2px solid #e2e8f0;
  border-top: none;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.quick-time-btn {
  flex: 1;
  min-width: 60px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.quick-time-btn:hover {
  background: #f8fafc;
  border-color: #3b82f6;
  color: #3b82f6;
}

.quick-time-btn:active {
  background: #3b82f6;
  color: white;
  transform: scale(0.98);
}

/* 确保输入框容器有相对定位以支持图标的绝对定位 */
.time-field .form-input[type="datetime-local"] {
  position: relative;
}

.time-field .form-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

/* 日期时间输入框样式优化 */
.time-field .form-input[type="datetime-local"] {
  width: 100%;
  padding: 16px 50px 16px 20px; /* 右侧留出图标空间 */
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background: #ffffff;
  transition: all 0.3s ease;
  outline: none;
  color: #374151;
  font-family: inherit;
  cursor: pointer;
}

.time-field .form-input[type="datetime-local"]:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: white;
}

.time-field .form-input[type="datetime-local"]:hover {
  border-color: #cbd5e1;
  background: white;
}

/* 文本类型时间输入框样式 */
.time-field .form-input[type="text"] {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background: #ffffff;
  transition: all 0.3s ease;
  outline: none;
  color: #374151;
  font-family: inherit;
  cursor: pointer;
}

.time-field .form-input[type="text"]:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: white;
}

.time-field .form-input[type="text"]:hover {
  border-color: #cbd5e1;
  background: white;
}

/* 完全隐藏默认的 yyyy/mm/dd 占位符 */
.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-text {
  color: transparent !important;
  width: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-month-field {
  color: transparent !important;
  width: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-day-field {
  color: transparent !important;
  width: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-year-field {
  color: transparent !important;
  width: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-hour-field {
  color: transparent !important;
  width: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-minute-field {
  color: transparent !important;
  width: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* 隐藏分隔符和包装器 */
.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-fields-wrapper {
  padding: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* 隐藏所有可能的日期时间编辑元素 */
.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit {
  color: transparent !important;
  background: transparent !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* 强制隐藏输入框内容 */
.time-field .form-input[type="datetime-local"] {
  color: transparent !important;
  text-shadow: none !important;
  -webkit-text-fill-color: transparent !important;
}

/* 针对Firefox的样式 */
.time-field .form-input[type="datetime-local"]::-moz-placeholder {
  color: transparent;
  opacity: 0;
}

.time-field .form-input[type="datetime-local"]::placeholder {
  color: transparent;
  opacity: 0;
}

/* 通用方法：当输入框为空时使用背景图片覆盖 */
.time-field .form-input[type="datetime-local"]:placeholder-shown {
  color: transparent;
}

/* 确保在所有状态下都隐藏默认占位符 */
.time-field .form-input[type="datetime-local"]:not(:focus):invalid {
  color: transparent;
}

/* 当输入框为空时完全隐藏内容 */
.time-field .form-input[type="datetime-local"]:invalid {
  color: transparent;
}

/* 当有值时显示正常内容 */
.time-field .form-input[type="datetime-local"]:valid {
  color: #374151;
}

.time-field .form-input[type="datetime-local"]:valid::-webkit-datetime-edit-text {
  color: #374151;
  width: auto;
}

.time-field .form-input[type="datetime-local"]:valid::-webkit-datetime-edit-month-field {
  color: #374151;
  width: auto;
}

.time-field .form-input[type="datetime-local"]:valid::-webkit-datetime-edit-day-field {
  color: #374151;
  width: auto;
}

.time-field .form-input[type="datetime-local"]:valid::-webkit-datetime-edit-year-field {
  color: #374151;
  width: auto;
}

.time-field .form-input[type="datetime-local"]:valid::-webkit-datetime-edit-hour-field {
  color: #374151;
  width: auto;
}

.time-field .form-input[type="datetime-local"]:valid::-webkit-datetime-edit-minute-field {
  color: #374151;
  width: auto;
}

/* 聚焦时也显示内容 */
.time-field .form-input[type="datetime-local"]:focus {
  color: #374151;
}

.time-field .form-input[type="datetime-local"]:focus::-webkit-datetime-edit-text,
.time-field .form-input[type="datetime-local"]:focus::-webkit-datetime-edit-month-field,
.time-field .form-input[type="datetime-local"]:focus::-webkit-datetime-edit-day-field,
.time-field .form-input[type="datetime-local"]:focus::-webkit-datetime-edit-year-field,
.time-field .form-input[type="datetime-local"]:focus::-webkit-datetime-edit-hour-field,
.time-field .form-input[type="datetime-local"]:focus::-webkit-datetime-edit-minute-field {
  color: #374151;
  width: auto;
}

/* 添加自定义文字显示 - 替换默认占位符 */
.time-field::before {
  content: attr(data-label);
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  pointer-events: none;
  font-size: 14px;
  z-index: 10;
  line-height: 1;
  font-weight: 400;
  transition: color 0.2s ease;
  background: transparent;
}

/* 当输入框聚焦时改变文字颜色 */
.time-field:has(.form-input[type="datetime-local"]:focus)::before,
.time-field:has(.form-input[type="text"]:focus)::before {
  color: #3b82f6;
}

/* 当有值时显示不同的样式 */
.time-field:has(.form-input[type="datetime-local"]:valid)::before,
.time-field:has(.form-input[type="text"]:not(:placeholder-shown))::before {
  color: #374151;
  font-weight: 500;
}

/* 添加选中日期显示 */
.time-field::after {
  content: attr(data-selected-date);
  position: absolute;
  left: 20px; /* 与标签文字相同位置，最左边 */
  top: 50%;
  transform: translateY(-50%);
  color: #374151;
  pointer-events: none;
  font-size: 14px;
  z-index: 10;
  line-height: 1;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.2s ease;
  background: transparent;
}

/* 当有值时显示选中的日期 */
.time-field.has-selected-date::after {
  opacity: 1;
}

/* 当有选中日期时隐藏标签文字 */
.time-field.has-selected-date::before {
  opacity: 0;
}

/* 确保文字不会干扰点击 */
.time-field::before {
  pointer-events: none;
}

/* 增强点击区域 */
.time-field {
  cursor: pointer;
}

.time-field:hover .form-input[type="datetime-local"],
.time-field:hover .form-input[type="text"] {
  border-color: #cbd5e1;
}

/* 日期选择器图标样式 - 移动到最右边 */
.time-field .form-input[type="datetime-local"]::-webkit-calendar-picker-indicator {
  opacity: 0.7;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  margin: 0;
  background-color: transparent;
  z-index: 15;
  border: none;
  outline: none;
}

.time-field .form-input[type="datetime-local"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
  background: rgba(59, 130, 246, 0.1);
}

/* 加载动画样式 - 覆盖全局样式，确保居中显示 */
.template-selection-modal .loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6b7280;
  text-align: center;
  width: 100%;
  height: 160px;
}

.template-selection-modal .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 轮播组件样式 */
.work-image-container .ant-carousel {
  position: relative;
  height: 130px;
  border-radius: 8px;
  overflow: hidden;
}

.work-image-container .ant-carousel .slick-slide {
  height: 130px;
}

.work-image-container .ant-carousel .slick-slide > div {
  height: 100%;
}

.work-image-container .work-image {
  height: 130px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

/* 默认轮播箭头样式优化 */
.work-image-container .ant-carousel .slick-prev,
.work-image-container .ant-carousel .slick-next {
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.work-image-container:hover .ant-carousel .slick-prev,
.work-image-container:hover .ant-carousel .slick-next {
  opacity: 1;
}

.work-image-container .ant-carousel .slick-prev:before,
.work-image-container .ant-carousel .slick-next:before {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
}

.work-image-container .ant-carousel .slick-prev:hover:before,
.work-image-container .ant-carousel .slick-next:hover:before {
  color: white;
}

/* 自定义轮播点样式 */
.work-image-container .custom-dots {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  display: flex !important;
  gap: 4px;
  z-index: 10;
}

.work-image-container .custom-dots li {
  width: 6px;
  height: 6px;
  margin: 0;
}

.work-image-container .custom-dots li button {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
  padding: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.work-image-container .custom-dots li.slick-active button {
  background: white;
  transform: scale(1.2);
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
}

/* 当只有一张图片时隐藏轮播点 */
.work-image-container .ant-carousel .slick-dots li:only-child {
  display: none !important;
}

/* 当只有一个轮播点时隐藏整个点容器 */
.work-image-container .ant-carousel .slick-dots:has(li:only-child) {
  display: none !important;
}

/* 轮播过渡动画优化 */
.work-image-container .ant-carousel .slick-slide {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.work-image-container .ant-carousel .slick-track {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 防止轮播跳跃时的闪烁 */
.work-image-container .ant-carousel .slick-list {
  overflow: hidden;
}

.work-image-container .ant-carousel .slick-slide img {
  transition: opacity 0.2s ease-in-out;
}

/* 当轮播切换时保持图片稳定 */
.work-image-container .ant-carousel .slick-slide.slick-current img {
  opacity: 1;
}

.work-image-container .ant-carousel .slick-slide:not(.slick-current) img {
  opacity: 0.95;
}

/* 图片标签样式 */
.work-image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
  padding: 8px 12px;
  font-size: 12px;
}

.work-image-label {
  font-size: 12px;
  color: white;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-row {
    flex-direction: column;
    gap: 12px;
  }

  .time-field .form-input[type="datetime-local"] {
    padding: 14px 50px 14px 16px; /* 移动端也保留图标空间 */
    font-size: 16px; /* 防止iOS缩放 */
  }

  .quick-time-selector {
    padding: 12px;
    gap: 10px;
  }

  .quick-time-btn {
    min-width: 70px;
    padding: 10px 14px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .template-selection-modal {
    min-width: 280px;
    max-width: 98%;
    height: 85vh;
  }

  .template-selection-modal .modal-content-body {
    padding: 16px 20px 80px 20px;
  }

  /* 小屏幕连接线样式已删除 */

  .distribution-card {
    height: 50px;
    font-size: 12px;
  }

  .template-options {
    flex-direction: column;
    gap: 8px;
  }

  .template-option {
    padding: 12px;
  }

  .option-title {
    font-size: 13px;
  }

  .option-desc {
    font-size: 11px;
  }

  .template-selected {
    padding: 12px;
  }

  .template-name {
    font-size: 13px;
  }

  .time-field .form-input[type="datetime-local"] {
    padding: 12px 45px 12px 14px;
    font-size: 16px;
  }

  .quick-time-selector {
    padding: 10px;
    gap: 8px;
  }

  .quick-time-btn {
    min-width: 60px;
    padding: 8px 12px;
    font-size: 12px;
  }
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/workbench/components/TemplateManagement.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/* 详细页面样式 */
.detail-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  /* border-bottom: 1px solid #e2e8f0; */
}

.back-btn {
  padding: 8px 16px;
  background: transparent;
  color: #3b82f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.back-btn:hover {
  background: #f0f9ff;
  border-color: #3b82f6;
}

.detail-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

/* 模板卡片基础样式 - 参考分配模板模态框设计 */
.template-card {
  background: white;
  border: 1px solid #f0f1f3;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  padding: 0;
  min-height: 96px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.template-card:hover {
  border-color: #e5e7eb;
  background-color: #fafbfc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.template-card-content {
  display: flex;
  align-items: center;
  padding: 18px 20px;
  gap: 16px;
  width: 100%;
  min-height: 96px;
  overflow: hidden;
}

.template-card-icon {
  width: 48px;
  height: 48px;
  background: #f8fafc;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 20px;
  color: #64748b;
}

.template-card-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.template-card-title {
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.4;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.template-card-subtitle {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.template-card-description {
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.template-card-tags {
  display: flex;
  gap: 6px;
  margin-top: 2px;
  margin-bottom: 2px;
  flex-wrap: wrap;
  align-items: center;
}

.template-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.template-tag.official {
  background: #fef3c7;
  color: #d97706;
}

.template-tag.default {
  background: linear-gradient(135deg, #FFB800, #FFA500);
  color: white;
  box-shadow: 0 2px 4px rgba(255, 184, 0, 0.3);
}

.template-tag.my-template {
  background: #dbeafe;
  color: #2563eb;
}

.template-tag.usage {
  background: #f0fdf4;
  color: #16a34a;
}

/* 默认模板样式 - 简洁设计，无特殊动效 */
.template-card.default-template {
  /* 保持与普通卡片相同的样式 */
}

.template-card.default-template .template-card-icon {
  background: linear-gradient(135deg, #FFB800, #FFA500);
  color: white;
}

/* 官方模板图标样式 */
.template-card-icon.official {
  background: #fef3c7;
  color: #d97706;
}

/* 我的模板图标样式 */
.template-card-icon.my-template {
  background: #dbeafe;
  color: #2563eb;
}

/* 加载状态 */
.loading-card {
  opacity: 0.7;
  pointer-events: none;
}

.loading-card .loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 详细模板卡片样式 */
.detail-template-card {
  transition: all 0.3s ease;
}

.detail-template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.detail-template-card .template-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.detail-template-card .usage-info {
  font-size: 12px;
  color: #64748b;
}

.detail-template-card .use-template-btn {
  padding: 6px 12px;
  background: #f97316;
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.detail-template-card .use-template-btn:hover {
  background: #ea580c;
}

/* 模板网格布局 - 简洁列表式设计 */
.template-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0;
}

/* 我的模板网格 - 保持列表式布局 */
.template-grid.my-templates {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 文件夹网格 - 简洁列表式设计 */
.folder-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 0;
}

/* 模板列表 - 文件夹内的模板 */
.template-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 模板卡片基础样式 */
.template-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 80px;
}

.template-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.template-card.editable {
  cursor: pointer;
}

.template-icon {
  margin-bottom: 0;
  flex-shrink: 0;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 4px;
  width: 32px;
  height: 32px;
}

.icon-dot {
  background: #3b82f6;
  border-radius: 4px;
}

.template-info {
  flex: 1;
}

.template-title {
  margin-bottom: 0;
}

.template-name-with-badges {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.template-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.template-type {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

.template-type.official {
  background: #fbbf24;
  color: white;
}

.current-template-badge {
  padding: 2px 8px;
  background: #8b5cf6;
  color: white;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

.template-meta {
  margin-bottom: 8px;
}

.create-time {
  font-size: 12px;
  color: #64748b;
}

.edit-hint {
  font-size: 12px;
  color: #64748b;
  margin-top: 8px;
}

.template-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0;
  flex-shrink: 0;
}

.usage-info {
  font-size: 12px;
  color: #64748b;
}

.use-template-btn {
  padding: 8px 16px;
  background: #f97316;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.use-template-btn:hover {
  background: #ea580c;
}

.use-template-btn.current {
  background: #8b5cf6;
}

.use-template-btn.current:hover {
  background: #7c3aed;
}

/* 加载和空状态样式 */
.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #64748b;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #64748b;
  font-size: 14px;
}

/* 文件夹相关样式 - 网格布局，一行两个 */
.folder-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 16px 0;
}

/* 特殊模板可以跨列显示（可选） */
/* .folder-grid .folder-card:first-child {
  grid-column: 1 / -1;
} */

.folder-card {
  background: white;
  border-radius: 16px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  height: 100px;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  width: 100%;
  min-width: 0;
}

.folder-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
  border-color: #93c5fd;
}

.folder-card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* 文件夹左侧内容区域 */
.folder-card-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

/* 文件夹图标容器 */
.folder-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.folder-card:hover .folder-icon {
  background: linear-gradient(135deg, #FDE68A 0%, #FBBF24 100%);
  transform: scale(1.05);
}

/* 文件夹图标SVG样式 */
.folder-icon svg {
  transition: all 0.3s ease;
}

.folder-card:hover .folder-icon svg {
  transform: scale(1.1);
}

/* 特殊模板图标样式 */
.folder-icon.special-icon {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.folder-card:hover .folder-icon.special-icon {
  background: linear-gradient(135deg, #fde68a 0%, #fbbf24 100%);
}

/* 文件夹信息区域 */
.folder-info {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.folder-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.folder-card:hover .folder-name {
  color: #3b82f6;
}

.folder-count {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 官方标签样式 */
.official-tag,
.recommend-tag {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

/* 使用此模板按钮样式 */
.use-template-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 0.3px;
  flex-shrink: 0;
  white-space: nowrap;
  min-width: fit-content;
}

.use-template-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateX(2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 当前使用状态的按钮样式 */
.use-template-btn.current {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.use-template-btn.current:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateX(2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* 响应式设计 */
@media (min-width: 1400px) {
  .folder-grid {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
  }
}

@media (max-width: 1024px) {
  .folder-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .folder-grid {
    grid-template-columns: 1fr;
    gap: 8px;
    padding: 12px 0;
  }

  .folder-card {
    padding: 12px;
    height: 80px;
  }

  .folder-icon {
    width: 40px;
    height: 40px;
  }

  .folder-name {
    font-size: 13px;
  }

  .folder-count {
    font-size: 11px;
  }

  .official-tag,
  .recommend-tag {
    font-size: 9px;
    padding: 1px 6px;
  }

  .use-template-btn {
    padding: 4px 12px;
    font-size: 11px;
  }
}

/* 重复样式已在上面定义，这里删除 */

.folder-usage {
  margin-top: 8px;
}

.usage-count {
  font-size: 12px;
  color: #6b7280;
}

.folder-header {
  display: flex;
  align-items: center;
  gap: 12px;
  /* margin-bottom: 20px; */
  padding: 16px 0;
  /* border-bottom: 1px solid #e5e7eb; */
}

.folder-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.template-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 20px 0;
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/workbench/components/ClassManagement.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/* 班级管理页面专用样式 */
.class-management-container {
    padding: 0;
    background: transparent;
    height: 100%;
    overflow: visible;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}

.class-management-content {
    max-width: none;
    margin: 0;
    padding: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}

.class-management-header {
    padding:20px;
    text-align: left;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
    background: white;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.class-management-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    text-align: left;
    line-height: 1.2;
    position: relative;
}

.class-management-header h2::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 48px;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
    border-radius: 2px;
}

/* 添加班级按钮样式 */
.add-class-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 12px rgba(59, 130, 246, 0.2),
        0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.3px;
}

.add-class-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.add-class-btn:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 20px rgba(59, 130, 246, 0.3),
        0 4px 8px rgba(0, 0, 0, 0.1);
}

.add-class-btn:hover::before {
    opacity: 1;
}

.add-class-btn:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

.add-class-btn svg {
    transition: transform 0.3s ease;
}

.add-class-btn:hover svg {
    transform: scale(1.1) rotate(90deg);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .class-management-header {
        padding: 16px 16px 0 16px;
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .add-class-btn {
        padding: 10px 20px;
        font-size: 13px;
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .class-management-header {
        padding: 12px 12px 0 12px;
        gap: 12px;
    }

    .class-management-header h2 {
        font-size: 20px;
    }

    .add-class-btn {
        padding: 8px 16px;
        font-size: 12px;
        gap: 6px;
        width: 100%;
        justify-content: center;
    }

    .add-class-btn svg {
        width: 16px;
        height: 16px;
    }
}

/* 班级网格布局 - 优化版本 - 仅限班级管理页面 */
.class-management-container .classes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    margin: 0;
    padding: 20px;
    justify-content: start;
    align-content: start;
    width: 100%;
    overflow: visible;
}

/* 班级网格滚动条样式 - 仅限班级管理页面 */
.class-management-container .classes-grid::-webkit-scrollbar {
    width: 8px;
}

.class-management-container .classes-grid::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    margin: 10px 0;
}

.class-management-container .classes-grid::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.4);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.class-management-container .classes-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.6);
}

/* 班级卡片样式 - 现代化设计 */
.class-management-container .class-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid #f1f3f4;
    transition: all 0.2s ease;
    position: relative;
    min-height: 140px;
    height: auto;
    display: flex;
    flex-direction: column;
    cursor: pointer;
}

.class-management-container .class-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
    border-color: #e0e7ff;
}

.class-management-container .class-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.class-management-container .school-tag {
    background: #f0f4ff;
    color: #4a6fff;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
    border: 1px solid #e0e7ff;
}

.class-management-container .school-tag::before {
    content: "🏫";
    font-size: 12px;
}

.class-management-container .settings-icon {
    color: #9ca3af;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 6px;
    border-radius: 6px;
    background: transparent;
}

.class-management-container .settings-icon:hover {
    color: #4a6fff;
    background: #f0f4ff;
    transform: rotate(90deg);
}

.class-management-container .class-card-content {
    text-align: left;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 12px;
}

.class-management-container .class-name {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.class-management-container .student-count-section {
    background: #f8fafc;
    border-radius: 8px;
    padding: 12px 16px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.class-management-container .class-card:hover .student-count-section {
    background: #f0f4ff;
    border-color: #c7d2fe;
}

.class-management-container .student-count-number {
    font-size: 24px;
    font-weight: 700;
    color: #4a6fff;
    line-height: 1;
    margin: 0;
}

.class-management-container .student-count-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
    margin: 0;
}

/* 助教标识 */
.class-management-container .assistant-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 700;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.4);
    z-index: 10;
}

/* 无学校选择状态 */
.class-management-container .no-school-selected {
    text-align: center;
    padding: 60px 40px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    margin: 40px;
    border: 2px dashed #e5e7eb;
}

.class-management-container .no-school-icon {
    color: #9ca3af;
    margin-bottom: 20px;
}

.class-management-container .no-school-selected h3 {
    font-size: 20px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 12px 0;
}

.class-management-container .no-school-selected p {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
    line-height: 1.5;
}

/* 加载和错误状态 */
.class-management-container .loading-container,
.class-management-container .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 40px;
    margin: 40px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.class-management-container .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.class-management-container .loading-container p {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
}

.class-management-container .error-message {
    color: #ef4444;
    font-size: 16px;
    margin: 0;
    text-align: center;
}

/* 无班级状态样式 */
.class-management-container .no-classes {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 40px;
    margin: 40px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 24px;
    border: 2px dashed #cbd5e1;
    position: relative;
    overflow: hidden;
    min-height: 400px;
}

.class-management-container .no-classes::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.03) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(1deg); }
}

.class-management-container .no-classes-illustration {
    margin-bottom: 32px;
    position: relative;
    z-index: 2;
}

.class-management-container .empty-classroom {
    width: 120px;
    height: 80px;
    position: relative;
    margin: 0 auto;
}

.class-management-container .classroom-board {
    width: 80px;
    height: 40px;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-radius: 4px;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.class-management-container .classroom-board::after {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    border: 1px dashed #6b7280;
    border-radius: 2px;
}

.class-management-container .classroom-desks {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    width: 60px;
}

.class-management-container .desk {
    width: 24px;
    height: 16px;
    background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
    border-radius: 2px;
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.class-management-container .desk::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 2px;
    right: 2px;
    height: 2px;
    background: #9ca3af;
    border-radius: 1px;
}

.class-management-container .no-classes-content {
    text-align: center;
    position: relative;
    z-index: 2;
}

.class-management-container .no-classes-content h3 {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 16px 0;
    line-height: 1.3;
}

.class-management-container .no-classes-content p {
    font-size: 16px;
    color: #64748b;
    margin: 0 0 32px 0;
    line-height: 1.6;
    max-width: 400px;
}

.class-management-container .school-highlight {
    color: #3b82f6;
    font-weight: 600;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    padding: 2px 8px;
    border-radius: 6px;
    border: 1px solid #93c5fd;
}

.class-management-container .no-classes-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.class-management-container .create-first-class-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 16px 32px;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border: none;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 8px 24px rgba(59, 130, 246, 0.25),
        0 4px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.3px;
}

.class-management-container .create-first-class-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.class-management-container .create-first-class-btn:hover {
    transform: translateY(-3px);
    box-shadow:
        0 12px 32px rgba(59, 130, 246, 0.35),
        0 6px 12px rgba(0, 0, 0, 0.1);
}

.class-management-container .create-first-class-btn:hover::before {
    opacity: 1;
}

.class-management-container .create-first-class-btn:active {
    transform: translateY(-1px);
    transition: transform 0.1s ease;
}

.class-management-container .create-first-class-btn svg {
    transition: transform 0.3s ease;
}

.class-management-container .create-first-class-btn:hover svg {
    transform: scale(1.1) rotate(90deg);
}

.class-management-container .help-text {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-radius: 12px;
    font-size: 14px;
    color: #475569;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .class-management-container .classes-grid {
        grid-template-columns: 1fr;
        padding: 16px;
        gap: 16px;
    }

    .class-management-container .class-card {
        padding: 20px;
        min-height: 160px;
    }

    .class-management-container .class-name {
        font-size: 18px;
    }

    .class-management-container .student-count-number {
        font-size: 32px;
    }

    .class-management-container .no-classes {
        margin: 20px;
        padding: 40px 24px;
        min-height: 320px;
    }

    .class-management-container .no-classes-content h3 {
        font-size: 20px;
    }

    .class-management-container .no-classes-content p {
        font-size: 14px;
        margin-bottom: 24px;
    }

    .class-management-container .create-first-class-btn {
        padding: 14px 28px;
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .class-management-header {
        padding: 16px 0 0 16px;
    }

    .class-management-container .classes-grid {
        padding: 12px;
    }

    .class-management-container .class-card {
        padding: 16px;
        min-height: 140px;
    }

    .class-management-container .no-classes {
        margin: 12px;
        padding: 32px 20px;
        min-height: 280px;
    }

    .class-management-container .empty-classroom {
        width: 100px;
        height: 70px;
    }

    .class-management-container .classroom-board {
        width: 70px;
        height: 35px;
    }

    .class-management-container .classroom-desks {
        width: 50px;
    }

    .class-management-container .desk {
        width: 20px;
        height: 14px;
    }

    .class-management-container .no-classes-content h3 {
        font-size: 18px;
        margin-bottom: 12px;
    }

    .class-management-container .no-classes-content p {
        font-size: 13px;
        margin-bottom: 20px;
    }

    .class-management-container .create-first-class-btn {
        padding: 12px 24px;
        font-size: 14px;
        gap: 8px;
    }

    .class-management-container .help-text {
        padding: 10px 16px;
        font-size: 12px;
    }
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/workbench/components/ClassDetail.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
.class-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.class-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height:80px;
  background: white;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  padding: 20px;
}

/* 默认隐藏紧凑版学生信息和班级信息 */
.student-info-compact,
.class-info-compact {
  display: none;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 8px 16px;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}



.class-detail-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: calc(100vh - 80px); /* 减去头部高度 */
}

.left-section {
  flex: 0 0 300px;
  background: #ffffff;
  padding: 22px 24px 0  24px;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px); /* 减去头部高度 */
  overflow: hidden;
}



/* 班级信息区域 - 简约专业设计 */
.class-info-section {
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  position: relative;
}

.class-info-content {
  position: relative;
}

.class-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.class-name-display {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
  color: #1e293b;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.class-edit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.class-edit-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
  transform: translateY(-1px);
}

.school-name-display {
  font-size: 14px;
  margin: 0;
  color: #64748b;
  font-weight: 400;
}

/* 旧的班级头像样式已移除，使用新的卡片样式 */

.search-box {
  position: relative;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  width: 100%;
  max-width: 360px;
  margin-bottom: 20px;
}

.search-box:hover {
  border-color: #c7d2fe;
}

.search-box:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-icon {
  color: #64748b;
  margin-right: 12px;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  border: none !important;
  outline: none !important;
  background: transparent;
  font-size: 14px;
  color: #374151;
  font-weight: 400;
  box-shadow: none !important;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.search-input::placeholder {
  color: #94a3b8;
}

.students-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.students-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  flex-shrink: 0;
  position: relative;
  overflow: visible;
}

.students-header h3 {
  color: #1e293b;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.students-actions {
  display: flex;
  gap: 8px;
  position: relative;
}

.more-actions-container {
  position: relative;
}

.add-student-btn {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #64748b;
  position: relative;
}

.add-student-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #3b82f6;
}



/* 批量操作按钮样式 */
.batch-actions-container {
  position: relative;
  display: inline-block;
}

.batch-actions-btn {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #64748b;
  padding: 0;
}

.batch-actions-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

.batch-actions-btn.active {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #1d4ed8;
}

/* 批量操作下拉菜单 */
.batch-actions-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 6px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  min-width: 180px;
  overflow: hidden;
}

.batch-action-item {
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: none;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #475569;
  font-size: 14px;
  text-align: left;
  font-weight: 500;
}

.batch-action-item:hover {
  background: #f8fafc;
  color: #334155;
}

.batch-action-item:first-child:hover {
  color: #dc2626;
}

/* 危险操作样式 */
.batch-action-item-danger {
  color: #EF4444 !important;
}

.batch-action-item-danger:hover {
  background: #fef2f2 !important;
  color: #dc2626 !important;
}

/* 下拉菜单样式 */
.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 180px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 100;
  overflow: hidden;
  animation: fadeIn 0.2s ease;
}



@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.dropdown-menu-items {
  padding: 6px;
}

.dropdown-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #475569;
  font-size: 14px;
  font-weight: 500;
}

.dropdown-menu-item:hover {
  background: #f8fafc;
  color: #334155;
}

.dropdown-menu-item.danger {
  color: #ef4444;
}

.dropdown-menu-item.danger:hover {
  background: #fef2f2;
}

.dropdown-menu-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: inherit;
}

.dropdown-menu-divider {
  height: 1px;
  background: #e2e8f0;
  margin: 6px 0;
}

.students-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  padding-bottom: 20px; /* 确保底部有足够空间 */
}

.student-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid transparent;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 6px;
  background: #ffffff;
}

.student-item:hover {
  background: #f8fafc;
  border-color: #e2e8f0;
}

.student-item.selected {
  background: #eff6ff;
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.student-item.selected .student-name {
  color: #1e40af;
  font-weight: 600;
}

.student-item.selected .student-avatar {
  box-shadow: 0 0 0 2px #3b82f6;
}

.student-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
}

.student-avatar .avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.student-avatar .avatar-fallback {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.student-info {
  flex: 1;
  min-width: 0;
}

.student-name {
  color: #1e293b;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.student-id {
  color: #64748b;
  font-size: 12px;
}







.right-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px); /* 减去头部高度 */
  overflow: hidden;
}

/* 学生信息头部 */
.student-info-header {
  background: white;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.student-avatar-large {
  flex-shrink: 0;
}

.student-avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
}

.student-details {
  flex: 1;
}

.student-name-large {
  color: #111827;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  transition: all 0.3s ease;
}

.student-id-large {
  color: #6b7280;
  font-size: 12px;
  transition: all 0.3s ease;
}

.delete-student-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: 1px solid #fecaca;
  background: #fef2f2;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #ef4444;
}

.delete-student-btn:hover:not(:disabled) {
  background: #fee2e2;
  border-color: #fca5a5;
}

.delete-student-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f9fafb;
  border-color: #e5e7eb;
  color: #9ca3af;
}

/* 功能区域容器 */
.functions-container {
  display: flex;
  flex: 2 0 auto;
  align-items: stretch;
}

.functions-section {
  flex: 0 0 240px;
  background: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid rgba(59, 130, 246, 0.1);
}

.functions-section h3 {
  color: #6b7280;
  font-size: 13px;
  font-weight: 500;
  margin: 0 0 12px 0;
}

.function-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  justify-content: flex-start;
}

.function-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border: none;
  border-radius: 28px;
  background: #3b82f6;
  color: white;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
  min-height: 56px;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.function-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

/* 统一使用蓝色主题 */
.function-btn.publish-task,
.function-btn.distribute-blocks,
.function-btn.distribute-energy,
.function-btn.exchange-tokens,
.function-btn.reset-password {
  background: #3b82f6;
}

.function-btn.publish-task:hover,
.function-btn.distribute-blocks:hover,
.function-btn.distribute-energy:hover,
.function-btn.exchange-tokens:hover,
.function-btn.reset-password:hover {
  background: #2563eb;
}

.function-icon {
  font-size: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.function-icon svg {
  width: 100%;
  height: 100%;
  color: currentColor;
}

.learning-status {
  flex: 1;
  background: white;
  padding: 16px;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.learning-status h3 {
  color: #6b7280;
  font-size: 13px;
  font-weight: 500;
  margin: 0 0 12px 0;
  flex-shrink: 0;
}

.status-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  min-height: 200px;
  height: auto;
}

/* C6+C7区域 - 利用flex-wrap实现智能换行布局 */
.bottom-section {
  background: white;
  padding: 16px;
  display: flex;
  flex-direction: column; /* 主轴为纵向 */
  flex-wrap: wrap; /* 关键：允许换行，高度不够时自动换到右侧 */
  border-top: 1px solid #e2e8f0;
  gap: 16px;
  min-height: 0;
  align-content: stretch; /* 让换行后的列能够拉伸填满容器 */
  min-width: 0;
  justify-content: center;
}

/* 当容器被标记为紧凑模式时，强制使用横向布局 */
.bottom-section.compact-mode {
  flex-direction: row !important;
}

.bottom-section.compact-mode .energy-progress-section,
.bottom-section.compact-mode .template-card-section {
  flex: 1 1 50% !important;
  margin-top: 0 !important;
}



/* 能量进度区域 */
.energy-progress-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 10px;
  border: 1px solid #e2e8f0;
  /* 关键修改：让元素能够自适应换行后的空间 */
  flex: 1 1 auto; /* 允许伸缩，换行后平分空间 */
  height: 70px; /* 固定高度，节省空间 */
  justify-content: center;
  min-width: 0; /* 允许收缩到最小宽度 */
  overflow: hidden; /* 防止内容溢出 */
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 班级详情页面专用的模板卡片样式 - 使用独特类名避免冲突 */
.class-detail-template-card {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 10px;
  border: 1px solid #e2e8f0;
  /* 关键修改：让元素能够自适应换行后的空间 */
  flex: 1 1 auto; /* 允许伸缩，换行后平分空间 */
  height: 70px; /* 固定高度，节省空间 */
  justify-content: center;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  min-width: 0; /* 允许收缩到最小宽度 */
  overflow: hidden; /* 防止内容溢出 */
}



.energy-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #475569;
  font-weight: 500;
  margin-bottom: 2px;
}

.energy-header-label {
  display: flex;
  align-items: center;
  gap: 4px;
}

.energy-progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.energy-progress-fill {
  height: 100%;
  background: #10b981;
  border-radius: 4px;
  transition: width 0.3s ease;
}





.template-card[style*="cursor: default"]:hover {
  border-color: #e5e7eb;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transform: none;
}

.template-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  min-height: 24px;
  width: 100%;
}

.class-detail-template-card .template-card-main {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
  flex: 1;
}

.class-detail-template-card .template-card-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.class-detail-template-card .template-card-icon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #3b82f6;
  flex-shrink: 0;
}

.class-detail-template-card .template-card-info {
  display: flex;
  flex-direction: column;
  gap: 1px;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.class-detail-template-card .template-card-title {
  font-size: 13px;
  font-weight: 500;
  color: #1e293b;
  margin: 0;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.class-detail-template-card .template-card-subtitle {
  font-size: 11px;
  color: #64748b;
  margin: 0;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.class-detail-template-card .template-card-badge {
  background: #fff7ed;
  color: #ea580c;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  border: 1px solid #fed7aa;
  flex-shrink: 0;
  height: 18px; /* 减少高度 */
  display: flex;
  align-items: center;
  white-space: nowrap;
}



.template-action:hover {
  text-decoration: underline;
}

.energy-section {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
}

.energy-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.energy-label {
  color: #9ca3af;
  font-size: 14px;
  font-weight: 400;
  margin: 0;
}

.energy-display {
  text-align: right;
}

.energy-available-number {
  color: #10b981;
  font-size: 16px;
  font-weight: 600;
}

.energy-total-number {
  color: #9ca3af;
  font-size: 16px;
  font-weight: 400;
}

.energy-bar {
  height: 12px;
  background: #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.energy-progress {
  height: 100%;
  background: #10b981;
  border-radius: 6px;
  transition: width 0.3s ease;
}

.template-section {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
  color: #6b7280;
  font-weight: 400;
}

.template-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  /* 默认样式（官方模板） */
  background: #fef3c7;
  color: #d97706;
}

/* 自定义模板样式 */
.template-badge.custom {
  background: #e0f2fe;
  color: #0277bd;
}

.template-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.template-icon {
  font-size: 20px;
  color: #f59e0b;
}

.template-info span {
  color: #111827;
  font-size: 14px;
  font-weight: 500;
}

/* 加载和错误状态样式 */
.loading-message,
.error-message,
.no-students-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
}

.loading-message p,
.error-message p,
.no-students-message p {
  margin: 0 0 16px 0;
  font-size: 14px;
}

.retry-button {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: #2563eb;
}

.template-description {
  margin-top: 8px;
  color: #9ca3af;
  font-size: 12px;
  line-height: 1.4;
}

.template-loading {
  color: #9ca3af !important;
  font-style: italic;
}

/* 分配能量弹窗样式 */
.assign-points-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
}

.assign-points-modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 24px;
}

.assign-points-modal .ant-modal-body {
  padding: 24px;
}

.assign-points-modal .ant-input-number {
  border-radius: 12px;
  border: 1px solid #d1d5db;
}

.assign-points-modal .ant-input-number:focus,
.assign-points-modal .ant-input-number-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.assign-points-modal .ant-alert {
  border-radius: 12px;
}

.assign-points-modal .ant-btn {
  border-radius: 8px;
  font-weight: 500;
}

.assign-points-modal .ant-btn-primary {
  background: #3b82f6;
  border-color: #3b82f6;
}

.assign-points-modal .ant-btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

/* 兑换密钥弹窗样式 */
.key-exchange-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
}

.key-exchange-modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 24px;
}

.key-exchange-modal .ant-modal-body {
  padding: 24px;
}

.key-exchange-modal .ant-upload-dragger {
  border-radius: 12px;
  border: 2px dashed #d1d5db;
  background: linear-gradient(to bottom, rgba(59, 130, 246, 0.05), white);
}

.key-exchange-modal .ant-upload-dragger:hover {
  border-color: #3b82f6;
}

.key-exchange-modal .ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.key-exchange-modal .ant-table-thead > tr > th {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
}

.key-exchange-modal .ant-btn {
  border-radius: 8px;
  font-weight: 500;
}

.key-exchange-modal .ant-btn-primary {
  background: #3b82f6;
  border-color: #3b82f6;
}

.key-exchange-modal .ant-btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.key-exchange-modal .ant-input {
  border-radius: 6px;
}

.key-exchange-modal .ant-input:focus,
.key-exchange-modal .ant-input-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.key-exchange-modal .ant-alert {
  border-radius: 12px;
}

.key-exchange-modal .ant-progress-circle {
  color: #3b82f6;
}

/* 任务发布成功提示样式 */
.ant-modal-confirm .ant-modal-confirm-title {
  color: #059669;
  font-weight: 600;
  font-size: 18px;
}

.ant-modal-confirm .ant-modal-confirm-content {
  margin-top: 12px;
  color: #374151;
  line-height: 1.6;
}

.ant-modal-confirm .ant-modal-confirm-content p {
  margin-bottom: 8px;
}

.ant-modal-confirm .ant-modal-confirm-content strong {
  color: #1f2937;
  font-weight: 600;
}

.ant-modal-confirm .ant-modal-confirm-btns {
  margin-top: 24px;
}

.ant-modal-confirm .ant-btn-primary {
  background: #059669;
  border-color: #059669;
  border-radius: 8px;
  font-weight: 500;
  padding: 8px 24px;
  height: auto;
}

.ant-modal-confirm .ant-btn-primary:hover {
  background: #047857;
  border-color: #047857;
}

/* 兑换密令弹窗样式 */
.redeem-key-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
}

.redeem-key-modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 24px;
}

.redeem-key-modal .ant-modal-body {
  padding: 24px;
}

.redeem-key-modal .ant-input {
  border-radius: 12px;
  border: 1px solid #d1d5db;
}

.redeem-key-modal .ant-input:focus,
.redeem-key-modal .ant-input-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.redeem-key-modal .ant-btn {
  border-radius: 8px;
  font-weight: 500;
}

.redeem-key-modal .ant-btn-primary {
  background: #3b82f6;
  border-color: #3b82f6;
}

.redeem-key-modal .ant-btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.redeem-key-modal .ant-result-title {
  color: #1f2937;
}

.redeem-key-modal .ant-result-subtitle {
  color: #6b7280;
}

/* 移除所有响应式设计代码 */

/* 移除所有fallback响应式代码 */

/* 移除所有响应式样式 */

/* 确保内容不会溢出 */
.class-detail-content * {
  box-sizing: border-box;
}

/* 滚动条样式优化 */
.students-list::-webkit-scrollbar,
.functions-section::-webkit-scrollbar,
.learning-status::-webkit-scrollbar,
.left-section::-webkit-scrollbar,
.class-detail-content::-webkit-scrollbar {
  width: 4px;
}

/* 响应式布局 - 与右侧抽屉同步的优化 */
@media (max-width: 1500px) {
  /* 当屏幕宽度不足时，将班级信息和学生信息头部移到顶部与返回按钮并排 */
  .class-detail-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 80px;
    gap: 0;
    background: white;
    padding: 0;
    overflow: hidden;
  }

  /* 学生列表宽度与融合头部对齐 - 返回按钮(80px) + 班级信息区域(240px) = 320px */
  .left-section {
    flex: 0 0 320px;
  }

  /* 隐藏左侧的班级信息区域 */
  .left-section .class-info-section {
    display: none;
  }

  /* 返回按钮融合样式 */
  .class-detail-header .back-button {
    background: transparent;
    border: none;
    border-radius: 0;
    padding: 12px 20px;
    margin: 0;
    flex-shrink: 0;
    width: 160px;
    box-sizing: border-box;
  }

  .class-detail-header .back-button:hover {
    background: #f8fafc;
  }

  /* 隐藏右侧区域的学生信息头部 */
  .right-section .student-info-header {
    display: none;
  }

  /* 在顶部显示班级信息 - 融合样式 */
  .class-detail-header .class-info-compact {
    display: flex;
    align-items: center;
    gap: 8px;
    background: transparent;
    border: none;
    border-radius: 0;
    border-right: 1px solid #e2e8f0;
    padding: 12px 14px;
    flex-shrink: 0;
    width: 160px;
    box-sizing: border-box;
  }

  .class-info-compact .class-details-small {
    flex: 1;
    min-width: 0;
  }

  .class-info-compact .class-name-small {
    color: #111827;
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .class-info-compact .school-name-small {
    color: #6b7280;
    font-size: 12px;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .class-info-compact .class-actions-small {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    flex-shrink: 0;
  }

  .class-info-compact .class-edit-btn-small,
  .class-info-compact .class-delete-btn-small {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    border: none;
    background: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
  }

  .class-info-compact .class-edit-btn-small {
    color: #64748b;
  }

  .class-info-compact .class-edit-btn-small:hover {
    background: none;
    color: #475569;
  }

  .class-info-compact .class-delete-btn-small {
    color: #ef4444;
  }

  .class-info-compact .class-delete-btn-small:hover {
    background: none;
    color: #dc2626;
  }

  /* 在顶部显示学生信息 - 融合样式 */
  .class-detail-header .student-info-compact {
    display: flex;
    align-items: center;
    gap: 12px;
    background: transparent;
    border: none;
    border-radius: 0;
    padding: 12px 16px;
    flex: 1;
    min-width: 0;
  }

  .student-info-compact .student-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
    flex-shrink: 0;
  }

  .student-info-compact .student-details-small {
    flex: 1;
    min-width: 0;
  }

  .student-info-compact .student-name-small {
    color: #111827;
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .student-info-compact .student-id-small {
    color: #6b7280;
    font-size: 12px;
    margin: 0;
  }

  .student-info-compact .delete-student-btn-small {
    width: 40px;
    height: 40px;
    border-radius: 0;
    border: none;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #ef4444;
    flex-shrink: 0;
    margin: 0;
  }

  .student-info-compact .delete-student-btn-small:hover {
    background: #fef2f2;
    color: #dc2626;
  }

  .student-info-compact .delete-student-btn-small:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    color: #9ca3af;
  }
}

.students-list::-webkit-scrollbar-track,
.functions-section::-webkit-scrollbar-track,
.learning-status::-webkit-scrollbar-track,
.left-section::-webkit-scrollbar-track,
.class-detail-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.students-list::-webkit-scrollbar-thumb,
.functions-section::-webkit-scrollbar-thumb,
.learning-status::-webkit-scrollbar-thumb,
.left-section::-webkit-scrollbar-thumb,
.class-detail-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.students-list::-webkit-scrollbar-thumb:hover,
.functions-section::-webkit-scrollbar-thumb:hover,
.learning-status::-webkit-scrollbar-thumb:hover,
.left-section::-webkit-scrollbar-thumb:hover,
.class-detail-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}



/* 移除所有移动端优化代码 */

/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./app/workbench/components/ClassTasks.module.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/* 班级任务页面样式 - 现代化蓝色主题设计 */
.ClassTasks_classTasksContainer__u0SPI {
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  padding: 0;
  background: #f8fafc;
  overflow: hidden;
}

/* 主布局容器 - 响应式设计 */
.ClassTasks_mainLayout__Phnco {
  display: flex;
  flex-direction: row;
  gap: 0;
  height: 100vh;
  margin: 0;
  max-width: 100vw;
  overflow: hidden;
  width: 100%;
}

/* 中等屏幕时保持左右布局 */
@media (min-width: 768px) {
  .ClassTasks_mainLayout__Phnco {
    flex-direction: row;
    gap: 0;
    height: 100vh;
  }
}

/* 小屏幕时改为上下布局 */
@media (max-width: 767px) {
  .ClassTasks_mainLayout__Phnco {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }
}

/* 左侧筛选区域 - 响应式设计 */
.ClassTasks_leftSidebar__agiWV {
  width: 320px;
  flex-shrink: 0;
  order: 0;
  min-width: 280px;
}

/* 收缩状态时的宽度 */
.ClassTasks_leftSidebar__agiWV.ClassTasks_collapsed__zt8yX {
  width: 60px;
  min-width: 60px;
}

/* 超大屏幕时使用更宽的侧边栏 */
@media (min-width: 1400px) {
  .ClassTasks_leftSidebar__agiWV {
    width: 360px;
    min-width: 320px;
  }
}

/* 大屏幕时标准宽度 */
@media (min-width: 1200px) and (max-width: 1399px) {
  .ClassTasks_leftSidebar__agiWV {
    width: 320px;
    min-width: 280px;
  }
}

/* 中等屏幕时调整宽度 */
@media (min-width: 768px) and (max-width: 1199px) {
  .ClassTasks_leftSidebar__agiWV {
    width: 280px;
    min-width: 250px;
  }

  .ClassTasks_leftSidebar__agiWV.ClassTasks_collapsed__zt8yX {
    width: 50px;
    min-width: 50px;
  }
}

/* 小屏幕时全宽 */
@media (max-width: 767px) {
  .ClassTasks_leftSidebar__agiWV {
    width: 100%;
    order: 1;
    min-width: auto;
  }

  .ClassTasks_leftSidebar__agiWV.ClassTasks_collapsed__zt8yX {
    width: 100%;
    min-width: auto;
  }
}

/* 收缩状态的侧边栏 */
.ClassTasks_collapsedSidebar__62Klj {
  width: 100%;
  height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  border-right: 1px solid #e2e8f0;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 20px;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

/* 展开按钮 */
.ClassTasks_expandBtn__0_1tt {
  background: #64748b;
  border: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(100, 116, 139, 0.2);
}

.ClassTasks_expandBtn__0_1tt:hover {
  background: #475569;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(100, 116, 139, 0.3);
}

/* 侧边栏头部 */
.ClassTasks_sidebarHeader__O94HB {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px 8px 16px;
  border-bottom: 1px solid #f1f5f9;
}

/* 侧边栏标题 */
.ClassTasks_sidebarTitle__ecF7J {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  letter-spacing: 0.025em;
}

/* 收缩按钮 */
.ClassTasks_sidebarHeader__O94HB .ClassTasks_collapseBtn__x_9OX {
  background: #64748b;
  border: none;
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #ffffff;
}

.ClassTasks_sidebarHeader__O94HB .ClassTasks_collapseBtn__x_9OX:hover {
  background: #475569;
  transform: translateY(-1px);
}

/* 右侧内容区域 - 响应式设计 */
.ClassTasks_rightContent__L_1MH {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  background: #f8fafc;
  border-radius: 0;
  order: 1;
  box-shadow: none;
  border: none;
  overflow: hidden;
  width: 100%; /* 确保占满剩余空间 */
  transition: width 0.3s ease; /* 添加平滑过渡 */
}

/* 小屏幕时全宽显示 */
@media (max-width: 767px) {
  .ClassTasks_rightContent__L_1MH {
    width: 100%;
    order: 2;
  }
}

/* 中等屏幕时的布局 */
@media (min-width: 768px) and (max-width: 1199px) {
  .ClassTasks_rightContent__L_1MH {
    flex: 1;
    min-width: 0;
    width: calc(100% - 280px); /* 减去侧边栏宽度 */
  }
}

/* 大屏幕时的布局 */
@media (min-width: 1200px) {
  .ClassTasks_rightContent__L_1MH {
    flex: 1;
    min-width: 0;
    width: calc(100% - 320px); /* 减去侧边栏宽度 */
  }
}

/* 超大屏幕时的布局 */
@media (min-width: 1400px) {
  .ClassTasks_rightContent__L_1MH {
    width: calc(100% - 360px); /* 减去侧边栏宽度 */
  }
}

/* 当侧边栏收缩时的特殊处理 */
.ClassTasks_mainLayout__Phnco .ClassTasks_leftSidebar__agiWV.ClassTasks_collapsed__zt8yX + .ClassTasks_rightContent__L_1MH {
  width: calc(100% - 60px) !important; /* 收缩时只减去60px */
  transition: width 0.3s ease;
}

/* 中等屏幕时侧边栏收缩的处理 */
@media (min-width: 768px) and (max-width: 1199px) {
  .ClassTasks_mainLayout__Phnco .ClassTasks_leftSidebar__agiWV.ClassTasks_collapsed__zt8yX + .ClassTasks_rightContent__L_1MH {
    width: calc(100% - 50px) !important;
  }
}

/* 大屏幕时侧边栏收缩的处理 */
@media (min-width: 1200px) {
  .ClassTasks_mainLayout__Phnco .ClassTasks_leftSidebar__agiWV.ClassTasks_collapsed__zt8yX + .ClassTasks_rightContent__L_1MH {
    width: calc(100% - 60px) !important;
  }
}

/* 确保右侧内容区域内的所有子元素都能正确适应宽度变化 */
.ClassTasks_rightContent__L_1MH > * {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* 小屏幕时调整顺序 */
@media (max-width: 767px) {
  .ClassTasks_rightContent__L_1MH {
    order: 2;
    min-width: auto;
    width: auto;
    flex: 1;
  }
}



/* 搜索和操作区域 */
.ClassTasks_actionSection__A1z9J {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  flex-shrink: 0;
  gap: 20px;
  min-height: 64px;
  box-sizing: border-box;
  height: 80px;
}

.ClassTasks_searchBox__RSbQy {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
}

.ClassTasks_searchIcon__HnXyr {
  position: absolute;
  left: 20px;
  color: #6b7280;
  z-index: 1;
  transition: all 0.2s ease;
}

.ClassTasks_searchInput__NVgPo {
  width: 100%;
  padding: 12px 20px 12px 56px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background: #f8fafc;
  color: #374151;
  transition: all 0.2s ease;
  font-weight: 400;
}

.ClassTasks_searchInput__NVgPo:hover {
  border-color: #c7d2fe;
  background: white;
}

.ClassTasks_searchInput__NVgPo:focus {
  outline: none;
  border-color: #4a6fff;
  background: white;
  box-shadow: 0 0 0 3px rgba(74, 111, 255, 0.1);
}

.ClassTasks_searchInput__NVgPo:focus + .ClassTasks_searchIcon__HnXyr {
  color: #4a6fff;
  transform: scale(1.1);
}

.ClassTasks_searchInput__NVgPo::placeholder {
  color: #94a3b8;
}

.ClassTasks_publishTaskBtn__1q9NK {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #4a6fff;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(74, 111, 255, 0.2);
}

.ClassTasks_publishTaskBtn__1q9NK:hover {
  background-color: #3b52cc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(74, 111, 255, 0.3);
}

.ClassTasks_publishTaskBtn__1q9NK:active {
  transform: translateY(0);
}

.ClassTasks_filterSection__2nPLz {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 0;
  height: 100vh;
  box-shadow: none;
  border: none;
  border-right: 1px solid #e2e8f0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 小屏幕时限制高度 */
@media (max-width: 767px) {
  .ClassTasks_filterSection__2nPLz {
    height: auto;
    max-height: 60vh;
    min-height: 200px;
  }
}

/* 中等屏幕时适中高度 */
@media (min-width: 768px) and (max-width: 1023px) {
  .ClassTasks_filterSection__2nPLz {
    height: 100vh;
    overflow-y: auto;
  }
}

/* 大屏幕时全高度 */
@media (min-width: 1024px) {
  .ClassTasks_filterSection__2nPLz {
    border-radius: 0;
    height: 100vh;
    overflow-y: auto;
  }
}

/* 筛选区域标题栏 */
.ClassTasks_filterHeader__Ps9Tz {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  flex-shrink: 0;
  min-height: 64px;
  box-sizing: border-box;
}

@media (min-width: 1024px) {
  .ClassTasks_filterHeader__Ps9Tz {
    padding: 12px 16px;
    justify-content: center;
    min-height: 64px;
    box-sizing: border-box;
  }
}

/* 筛选切换按钮 */
.ClassTasks_filterToggle__E4IsN {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ClassTasks_filterToggle__E4IsN:hover {
  background: #2563eb;
}

/* 大屏幕时隐藏切换按钮 */
@media (min-width: 1024px) {
  .ClassTasks_filterToggle__E4IsN {
    display: none;
  }
}

.ClassTasks_filterHeaderLeft__JJlWv {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.ClassTasks_filterTitle__6db7V {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  flex-shrink: 0;
  letter-spacing: 0.025em;
}

/* 选中的筛选条件显示 */
.ClassTasks_selectedFilters__RSTM0 {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  min-width: 0;
}

.ClassTasks_selectedTag__SBsrf {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #1e40af;
  border: 1px solid #3b82f6;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ClassTasks_noFilters__9BJ8P {
  font-size: 12px;
  color: #64748b;
  font-style: italic;
}



/* 筛选内容区域 */
.ClassTasks_filterContent__SYcF8 {
  padding: 12px 16px;
  flex: 1;
  opacity: 0;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  overflow: hidden;
}

/* 展开状态 */
.ClassTasks_filterContent__SYcF8.ClassTasks_expanded__Eh22W {
  opacity: 1;
  overflow-y: auto;
}

/* 小屏幕时更紧凑的内边距 */
@media (max-width: 767px) {
  .ClassTasks_filterContent__SYcF8 {
    padding: 8px 12px;
  }

  .ClassTasks_filterContent__SYcF8.ClassTasks_expanded__Eh22W {
    padding: 12px 16px;
  }
}

/* 中等屏幕时适中的内边距 */
@media (min-width: 768px) and (max-width: 1023px) {
  .ClassTasks_filterContent__SYcF8 {
    padding: 16px 20px;
    opacity: 1;
    overflow-y: auto;
  }
}

/* 大屏幕时始终展开 */
@media (min-width: 1024px) {
  .ClassTasks_filterContent__SYcF8 {
    padding: 20px 24px;
    opacity: 1;
    overflow-y: auto;
  }
}

.ClassTasks_filterRow__r5FT0 {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
  gap: 8px;
}

/* 小屏幕时更紧凑的间距 */
@media (max-width: 767px) {
  .ClassTasks_filterRow__r5FT0 {
    margin-bottom: 12px;
    gap: 6px;
  }
}

/* 中等屏幕时适中的间距 */
@media (min-width: 768px) and (max-width: 1023px) {
  .ClassTasks_filterRow__r5FT0 {
    margin-bottom: 18px;
    gap: 8px;
  }
}

/* 大屏幕时更宽松的间距 */
@media (min-width: 1024px) {
  .ClassTasks_filterRow__r5FT0 {
    margin-bottom: 24px;
    gap: 12px;
  }
}

.ClassTasks_filterRow__r5FT0:last-child {
  margin-bottom: 0;
}

.ClassTasks_filterLabel__QpIvN {
  font-size: 13px;
  color: #1e293b;
  font-weight: 700;
  letter-spacing: 0.025em;
}

@media (min-width: 1024px) {
  .ClassTasks_filterLabel__QpIvN {
    font-size: 15px;
  }
}

/* 筛选标签行（包含标签和折叠按钮） */
.ClassTasks_filterLabelRow__ZrEvi {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

/* 筛选标签内容区域 */
.ClassTasks_filterLabelContent__1LBjo {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

/* 选中状态提示文字 */
.ClassTasks_selectedHint__VN2Wy {
  font-size: 12px;
  color: #94a3b8;
  font-weight: 500;
  background: #f8fafc;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  white-space: nowrap;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (min-width: 1024px) {
  .ClassTasks_selectedHint__VN2Wy {
    font-size: 13px;
    max-width: 200px;
  }
}

/* 折叠按钮 */
.ClassTasks_collapseBtn__x_9OX {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ClassTasks_collapseBtn__x_9OX:hover {
  background: #f1f5f9;
}

/* 折叠图标 */
.ClassTasks_collapseIcon__yftIm {
  transition: transform 0.3s ease;
  color: #64748b;
}

.ClassTasks_collapseIcon__yftIm.ClassTasks_collapsed__zt8yX {
  transform: rotate(-90deg);
}

.ClassTasks_filterTabs__53qK_ {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
  align-items: center;
  animation: ClassTasks_fadeIn__LSMuf 0.3s ease;
  width: 100%;
}

/* 小屏幕时使用更紧凑的网格，最多2列 */
@media (max-width: 767px) {
  .ClassTasks_filterTabs__53qK_ {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 6px;
  }
}

/* 中等屏幕时使用适中的网格，最多3列 */
@media (min-width: 768px) and (max-width: 1023px) {
  .ClassTasks_filterTabs__53qK_ {
    grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
    gap: 8px;
  }
}

/* 大屏幕时使用更宽的网格，最多4列 */
@media (min-width: 1024px) and (max-width: 1399px) {
  .ClassTasks_filterTabs__53qK_ {
    grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
    gap: 10px;
  }
}

/* 超大屏幕时限制最大列数为5列 */
@media (min-width: 1400px) {
  .ClassTasks_filterTabs__53qK_ {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
  }
}

@keyframes ClassTasks_fadeIn__LSMuf {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ClassTasks_filterTab__T32CH {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #475569;
  white-space: nowrap;
  text-align: center;
  letter-spacing: 0.025em;
  width: 100%;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ClassTasks_filterTab__T32CH:hover {
  border-color: #93c5fd;
  color: #1e40af;
  background: #f8fafc;
  transform: translateY(-1px);
}

.ClassTasks_filterTab__T32CH.ClassTasks_active__VrffX {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  border-color: #93c5fd;
  color: #1e40af;
  transform: translateY(-1px);
}

/* 小屏幕时的筛选标签优化 */
@media (max-width: 767px) {
  .ClassTasks_filterTab__T32CH {
    padding: 6px 8px;
    font-size: 11px;
    border-radius: 8px;
  }
}

/* 中等屏幕时的筛选标签优化 */
@media (min-width: 768px) and (max-width: 1023px) {
  .ClassTasks_filterTab__T32CH {
    padding: 7px 10px;
    font-size: 11px;
    border-radius: 10px;
  }
}

/* 大屏幕时的筛选标签优化 */
@media (min-width: 1024px) {
  .ClassTasks_filterTab__T32CH {
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 12px;
  }
}

.ClassTasks_dateRange__Ujq12 {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.ClassTasks_dateInputContainer__16x_o {
  position: relative;
  display: block;
  flex: 1;
  min-width: 100px;
  max-width: 120px;
}

.ClassTasks_datetimeInput__Ti2cz {
  padding: 14px 40px 14px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  width: 100%;
  color: transparent;
  transition: all 0.3s ease;
  font-weight: 500;
}

/* 日期输入框样式（只选择日期，不包含时间） */
.ClassTasks_dateInput__uxMth {
  padding: 8px 28px 8px 10px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 12px;
  background: white;
  cursor: pointer;
  width: 100%;
  color: transparent;
  transition: all 0.3s ease;
  font-weight: 500;
}

.ClassTasks_dateInput__uxMth:hover {
  border-color: #93c5fd;
  background: #f8fafc;
}

.ClassTasks_dateInput__uxMth:focus {
  outline: none;
  border-color: #93c5fd;
  background: white;
}

/* 隐藏日期输入框的默认占位符 */
.ClassTasks_dateInput__uxMth::-webkit-datetime-edit {
  color: transparent;
  background: transparent;
}

.ClassTasks_dateInput__uxMth::-webkit-datetime-edit-text {
  color: transparent;
  padding: 0;
}

.ClassTasks_dateInput__uxMth::-webkit-datetime-edit-month-field,
.ClassTasks_dateInput__uxMth::-webkit-datetime-edit-day-field,
.ClassTasks_dateInput__uxMth::-webkit-datetime-edit-year-field {
  color: transparent;
  background: transparent;
  border: none;
  padding: 0;
  width: 0;
  opacity: 0;
}

/* 当有值时显示实际的日期 */
.ClassTasks_dateInput__uxMth[value]:not([value=""])::-webkit-datetime-edit {
  color: #333;
  background: transparent;
}

.ClassTasks_dateInput__uxMth[value]:not([value=""])::-webkit-datetime-edit-text {
  color: #333;
  padding: 0 2px;
}

.ClassTasks_dateInput__uxMth[value]:not([value=""])::-webkit-datetime-edit-month-field,
.ClassTasks_dateInput__uxMth[value]:not([value=""])::-webkit-datetime-edit-day-field,
.ClassTasks_dateInput__uxMth[value]:not([value=""])::-webkit-datetime-edit-year-field {
  color: #333;
  background: transparent;
  border: none;
  padding: 0 2px;
  width: auto;
  opacity: 1;
}

/* 聚焦时显示字段，便于编辑 */
.ClassTasks_dateInput__uxMth:focus::-webkit-datetime-edit {
  color: #333;
}

.ClassTasks_dateInput__uxMth:focus::-webkit-datetime-edit-text {
  color: #333;
}

.ClassTasks_dateInput__uxMth:focus::-webkit-datetime-edit-month-field,
.ClassTasks_dateInput__uxMth:focus::-webkit-datetime-edit-day-field,
.ClassTasks_dateInput__uxMth:focus::-webkit-datetime-edit-year-field {
  color: #333;
  width: auto;
  opacity: 1;
  cursor: text;
  user-select: text;
  -webkit-user-select: text;
}

/* 当输入框为空时，完全隐藏默认字符 */
.ClassTasks_dateInput__uxMth:not([value])::-webkit-datetime-edit,
.ClassTasks_dateInput__uxMth[value=""]::-webkit-datetime-edit {
  color: transparent !important;
  background: transparent !important;
}

.ClassTasks_dateInput__uxMth:not([value])::-webkit-datetime-edit-text,
.ClassTasks_dateInput__uxMth[value=""]::-webkit-datetime-edit-text,
.ClassTasks_dateInput__uxMth:not([value])::-webkit-datetime-edit-month-field,
.ClassTasks_dateInput__uxMth[value=""]::-webkit-datetime-edit-month-field,
.ClassTasks_dateInput__uxMth:not([value])::-webkit-datetime-edit-day-field,
.ClassTasks_dateInput__uxMth[value=""]::-webkit-datetime-edit-day-field,
.ClassTasks_dateInput__uxMth:not([value])::-webkit-datetime-edit-year-field,
.ClassTasks_dateInput__uxMth[value=""]::-webkit-datetime-edit-year-field {
  color: transparent !important;
  background: transparent !important;
  width: 0 !important;
  opacity: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 日期输入框的日历图标 */
.ClassTasks_dateInput__uxMth::-webkit-calendar-picker-indicator {
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  transition: background-color 0.15s ease;
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  opacity: 0.6;
}

.ClassTasks_dateInput__uxMth::-webkit-calendar-picker-indicator:hover {
  background-color: rgba(59, 130, 246, 0.1);
  opacity: 1;
}

/* 完全隐藏默认的日期时间占位符和字段 */
.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit {
  color: transparent;
  background: transparent;
}

.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-text {
  color: transparent;
  padding: 0;
}

.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-month-field,
.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-day-field,
.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-year-field,
.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-hour-field,
.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-minute-field {
  color: transparent;
  background: transparent;
  border: none;
  padding: 0;
  width: 0;
  opacity: 0;
}

/* 当有值时显示实际的日期时间 */
.ClassTasks_datetimeInput__Ti2cz[value]:not([value=""])::-webkit-datetime-edit {
  color: #333;
  background: transparent;
}

.ClassTasks_datetimeInput__Ti2cz[value]:not([value=""])::-webkit-datetime-edit-text {
  color: #333;
  padding: 0 2px;
}

.ClassTasks_datetimeInput__Ti2cz[value]:not([value=""])::-webkit-datetime-edit-month-field,
.ClassTasks_datetimeInput__Ti2cz[value]:not([value=""])::-webkit-datetime-edit-day-field,
.ClassTasks_datetimeInput__Ti2cz[value]:not([value=""])::-webkit-datetime-edit-year-field,
.ClassTasks_datetimeInput__Ti2cz[value]:not([value=""])::-webkit-datetime-edit-hour-field,
.ClassTasks_datetimeInput__Ti2cz[value]:not([value=""])::-webkit-datetime-edit-minute-field {
  color: #333;
  background: transparent;
  border: none;
  padding: 0 2px;
  width: auto;
  opacity: 1;
}

/* 聚焦时也显示字段，但只在有值的情况下 */
.ClassTasks_datetimeInput__Ti2cz:focus[value]:not([value=""])::-webkit-datetime-edit {
  color: #333;
}

.ClassTasks_datetimeInput__Ti2cz:focus[value]:not([value=""])::-webkit-datetime-edit-text {
  color: #333;
}

.ClassTasks_datetimeInput__Ti2cz:focus[value]:not([value=""])::-webkit-datetime-edit-month-field,
.ClassTasks_datetimeInput__Ti2cz:focus[value]:not([value=""])::-webkit-datetime-edit-day-field,
.ClassTasks_datetimeInput__Ti2cz:focus[value]:not([value=""])::-webkit-datetime-edit-year-field,
.ClassTasks_datetimeInput__Ti2cz:focus[value]:not([value=""])::-webkit-datetime-edit-hour-field,
.ClassTasks_datetimeInput__Ti2cz:focus[value]:not([value=""])::-webkit-datetime-edit-minute-field {
  color: #333;
  width: auto;
  opacity: 1;
}

/* 自定义占位符样式 */
.ClassTasks_customPlaceholder___8gW9 {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 11px;
  pointer-events: none;
  z-index: 1;
}

/* 额外的隐藏规则，确保所有默认字符都被隐藏 */
.ClassTasks_datetimeInput__Ti2cz::-webkit-inner-spin-button,
.ClassTasks_datetimeInput__Ti2cz::-webkit-clear-button {
  display: none;
}

.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-fields-wrapper {
  color: transparent;
}

/* 当输入框为空时，完全隐藏所有内容 */
.ClassTasks_datetimeInput__Ti2cz:not([value])::-webkit-datetime-edit,
.ClassTasks_datetimeInput__Ti2cz[value=""]::-webkit-datetime-edit {
  color: transparent !important;
  background: transparent !important;
}

.ClassTasks_datetimeInput__Ti2cz:not([value])::-webkit-datetime-edit-text,
.ClassTasks_datetimeInput__Ti2cz[value=""]::-webkit-datetime-edit-text,
.ClassTasks_datetimeInput__Ti2cz:not([value])::-webkit-datetime-edit-month-field,
.ClassTasks_datetimeInput__Ti2cz[value=""]::-webkit-datetime-edit-month-field,
.ClassTasks_datetimeInput__Ti2cz:not([value])::-webkit-datetime-edit-day-field,
.ClassTasks_datetimeInput__Ti2cz[value=""]::-webkit-datetime-edit-day-field,
.ClassTasks_datetimeInput__Ti2cz:not([value])::-webkit-datetime-edit-year-field,
.ClassTasks_datetimeInput__Ti2cz[value=""]::-webkit-datetime-edit-year-field,
.ClassTasks_datetimeInput__Ti2cz:not([value])::-webkit-datetime-edit-hour-field,
.ClassTasks_datetimeInput__Ti2cz[value=""]::-webkit-datetime-edit-hour-field,
.ClassTasks_datetimeInput__Ti2cz:not([value])::-webkit-datetime-edit-minute-field,
.ClassTasks_datetimeInput__Ti2cz[value=""]::-webkit-datetime-edit-minute-field {
  color: transparent !important;
  background: transparent !important;
  width: 0 !important;
  opacity: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
}

.ClassTasks_datetimeInput__Ti2cz:hover {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.ClassTasks_datetimeInput__Ti2cz:focus {
  outline: none;
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}



/* 日历图标样式优化 */
.ClassTasks_datetimeInput__Ti2cz::-webkit-calendar-picker-indicator {
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  transition: background-color 0.15s ease;
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  opacity: 0.6;
  background-size: 14px;
}

.ClassTasks_datetimeInput__Ti2cz::-webkit-calendar-picker-indicator:hover {
  background-color: rgba(59, 130, 246, 0.1);
  opacity: 1;
}

/* 确保整个输入框都可以点击 */
.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit {
  padding: 0;
  cursor: pointer;
  margin-right: 30px;
}

.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
  cursor: pointer;
}

.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-text {
  padding: 0 2px;
  cursor: pointer;
}

.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-month-field,
.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-day-field,
.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-year-field,
.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-hour-field,
.ClassTasks_datetimeInput__Ti2cz::-webkit-datetime-edit-minute-field {
  padding: 0 2px;
  cursor: pointer;
}

.ClassTasks_dateSeparator__D1GzL {
  color: #6b7280;
  font-size: 12px;
  margin: 0 4px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.ClassTasks_clearDateBtn__n6cx8 {
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  color: #64748b;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.ClassTasks_clearDateBtn__n6cx8:hover {
  background: #fee2e2;
  border-color: #fca5a5;
  color: #dc2626;
}

/* 剩余时间样式 */
.ClassTasks_remainingTime__g0k9e {
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
}

/* 正常剩余时间 */
.ClassTasks_remainingTimeNormal__8m4ml {
  color: #059669;
  background: #ecfdf5;
}

/* 紧急时间（1天内）*/
.ClassTasks_remainingTimeUrgent__klLSM {
  color: #dc2626;
  background: #fef2f2;
}

/* 已超时 */
.ClassTasks_remainingTimeOverdue__a0Qt2 {
  color: #dc2626;
  background: #fef2f2;
}

/* 已结束/已完成 */
.ClassTasks_remainingTimeFinished__0xG9y {
  color: #6b7280;
  background: transparent;
}

.ClassTasks_refreshButton__IhZnR {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 16px;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 16px;
  font-size: 15px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #475569;
  width: 100%;
  margin-top: 24px;
  letter-spacing: 0.025em;
}

.ClassTasks_refreshButton__IhZnR:hover {
  border-color: #93c5fd;
  color: #1e40af;
  background: #f8fafc;
  transform: translateY(-1px);
}

.ClassTasks_tasksTable__VUUsz {
  background: #f8fafc;
  border-radius: 0;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  width: 100%;
  position: relative;
}

/* 创建一个滚动容器包装表头和表体 */
.ClassTasks_tableScrollContainer__ZTMks {
  flex: 1;
  overflow: auto;
  width: 100%;
  min-width: 800px; /* 设置最小宽度确保表格可读性 */
}

/* 表格内容容器 */
.ClassTasks_tableContent__1jmKO {
  min-width: 800px;
  width: 100%;
}

/* 小屏幕时调整滚动容器 */
@media (max-width: 767px) {
  .ClassTasks_tableScrollContainer__ZTMks {
    min-width: 700px;
  }

  .ClassTasks_tableHeader__xB3Zi,
  .ClassTasks_tableRow__axU5d {
    grid-template-columns: 1fr 100px 120px 100px 180px;
    min-width: 700px;
  }

  .ClassTasks_tableBody__M47Sz {
    min-width: 700px;
  }

  .ClassTasks_tableContent__1jmKO {
    min-width: 700px;
  }
}

/* 小屏幕时的表格优化 */
@media (max-width: 767px) {
  .ClassTasks_tasksTable__VUUsz {
    min-width: 0;
    overflow-x: auto;
  }
}

/* 中等屏幕时的表格优化 */
@media (min-width: 768px) and (max-width: 1199px) {
  .ClassTasks_tasksTable__VUUsz {
    min-width: 0;
    width: 100%;
    overflow-x: auto;
  }
}

/* 大屏幕时的表格优化 */
@media (min-width: 1200px) {
  .ClassTasks_tasksTable__VUUsz {
    min-width: 0;
    width: 100%;
    overflow-x: auto;
  }
}

.ClassTasks_tableHeader__xB3Zi {
  display: grid;
  grid-template-columns: 1fr 120px 140px 120px 220px;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-bottom: 2px solid rgba(59, 130, 246, 0.1);
  font-weight: 700;
  color: #1e293b;
  width: 100%;
  min-width: 800px;
  position: sticky;
  top: 0;
  z-index: 10;
  flex-shrink: 0;
}



.ClassTasks_headerCell___9x5w {
  padding: 12px 8px;
  font-size: 12px;
  text-align: center;
  letter-spacing: 0.025em;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 48px;
}

@media (min-width: 1024px) {
  .ClassTasks_headerCell___9x5w {
    padding: 16px 12px;
    font-size: 14px;
    min-height: 56px;
  }
}

/* 任务名称表头左对齐 */
.ClassTasks_headerCell___9x5w:first-child {
  text-align: left;
  justify-content: flex-start;
}

.ClassTasks_tableBody__M47Sz {
  flex: 1;
  width: 100%;
  min-width: 800px;
}

.ClassTasks_tableRow__axU5d {
  display: grid;
  grid-template-columns: 1fr 120px 140px 120px 220px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  width: 100%;
  min-width: 800px;
}



.ClassTasks_tableRow__axU5d:hover {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.1);
}

.ClassTasks_tableRow__axU5d:last-child {
  border-bottom: none;
}

.ClassTasks_tableCell__XUeyK {
  padding: 12px 8px;
  font-size: 13px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #374151;
  min-height: 50px;
  overflow: hidden;
}

/* 操作栏单元格特殊处理 */
.ClassTasks_tableCell__XUeyK:last-child {
  align-items: center;
}

/* 大屏幕时使用更大的内边距 */
@media (min-width: 1024px) {
  .ClassTasks_tableCell__XUeyK {
    padding: 16px 12px;
    font-size: 14px;
    min-height: 60px;
  }
}

/* 任务名称列左对齐 */
.ClassTasks_tableCell__XUeyK:first-child {
  text-align: left;
  justify-content: flex-start;
  font-weight: 600;
  color: #1e293b;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/* 状态标签胶囊样式 - 深色描边、浅色背景、深色字体 */
.ClassTasks_statusBadge__H9GRg {
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.025em;
  border: 1.5px solid;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
}

/* 已完成状态 */
.ClassTasks_statusBadge__H9GRg.ClassTasks_completed____JAK {
  background: #f0f9ff;
  border-color: #0ea5e9;
  color: #0c4a6e;
}

/* 进行中状态 */
.ClassTasks_statusBadge__H9GRg.ClassTasks_inProgress__87Ubx {
  background: #eff6ff;
  border-color: #3b82f6;
  color: #1e40af;
}

/* 已结束状态 */
.ClassTasks_statusBadge__H9GRg.ClassTasks_ended__MVQ6O {
  background: #fef2f2;
  border-color: #ef4444;
  color: #991b1b;
}

/* 未开始状态 */
.ClassTasks_statusBadge__H9GRg.ClassTasks_notStarted__GS4_g {
  background: #fffbeb;
  border-color: #f59e0b;
  color: #92400e;
}

/* 默认状态 */
.ClassTasks_statusBadge__H9GRg.ClassTasks_default__V4q7P {
  background: #f8fafc;
  border-color: #64748b;
  color: #334155;
}

.ClassTasks_actions__mOwNc {
  display: inline-flex;
  gap: 0;
  justify-content: center;
  flex-wrap: nowrap;
  height: 32px;
  overflow: hidden;
  border: 1.5px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

/* 操作栏整体悬停效果 */
.ClassTasks_actions__mOwNc:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 大屏幕时使用更宽的操作栏 */
@media (min-width: 1024px) {
  .ClassTasks_actions__mOwNc {
    height: 36px;
    border-radius: 8px;
  }
}

.ClassTasks_actionBtn__icbC8 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  padding: 0 8px;
  border: none;
  background: transparent;
  border-radius: 0;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;
  max-width: 60px;
  height: 100%;
  white-space: nowrap;
  color: #475569;
  letter-spacing: 0.025em;
  flex: 1;
  position: relative;
}

/* 添加右侧分割线（除了最后一个按钮） */
.ClassTasks_actionBtn__icbC8:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 15%;
  bottom: 15%;
  width: 1px;
  background: linear-gradient(to bottom, transparent, #e2e8f0 20%, #e2e8f0 80%, transparent);
  opacity: 0.8;
}

/* 悬停效果 */
.ClassTasks_actionBtn__icbC8:hover {
  background: #f8fafc;
}

/* 查看按钮悬停效果 */
.ClassTasks_viewBtn__lwQOW:hover {
  background: #f0fdf4 !important;
  color: #10b981 !important;
}

/* 编辑按钮悬停效果 */
.ClassTasks_editBtn__Yhg3Y:hover {
  background: #fef3c7 !important;
  color: #d97706 !important;
}

/* 删除按钮悬停效果 */
.ClassTasks_deleteBtn__2THyT:hover {
  background: #fef2f2 !important;
  color: #dc2626 !important;
}

/* 大屏幕时使用更大的按钮 */
@media (min-width: 1024px) {
  .ClassTasks_actionBtn__icbC8 {
    gap: 2px;
    padding: 0 8px;
    font-size: 11px;
    min-width: 60px;
    max-width: 60px;
  }
}





/* 空状态样式 */
.ClassTasks_emptyState__tVXff {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
  text-align: center;
  background: white;
  border-radius: 16px;
  margin: 20px;
  border: 1px solid #e1e7ef;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.04);
}

.ClassTasks_emptyIcon__AHp0G {
  margin-bottom: 32px;
  opacity: 0.7;
  color: #64748b;
}

.ClassTasks_emptyTitle__9TMjt {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 12px;
}

.ClassTasks_emptyDescription__PMZmN {
  font-size: 15px;
  color: #64748b;
  line-height: 1.6;
  max-width: 360px;
}

/* 旋转动画 */
@keyframes ClassTasks_spin__UCHiT {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ClassTasks_refreshButton__IhZnR:disabled {
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

/* 额外的响应式优化 */
@media (max-width: 767px) {
  .ClassTasks_filterSection__2nPLz {
    height: auto;
    min-height: 300px;
  }

  .ClassTasks_filterTabs__53qK_ {
    grid-template-columns: 1fr;
  }

  .ClassTasks_actionSection__A1z9J {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .ClassTasks_searchBox__RSbQy {
    width: 100%;
  }
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./app/workbench/components/ClassProjects.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/* 班级项目组件样式 */

.ClassProjects_container__eRZ_8 {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

/* 头部区域 */
.ClassProjects_header__fa_t2 {
  margin-bottom: 24px;
}

.ClassProjects_headerLeft__1DGBJ {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ClassProjects_title__CJENO {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.ClassProjects_titleIcon__yr61F {
  color: #3b82f6;
}

.ClassProjects_subtitle__qmafY {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* 控制区域 */
.ClassProjects_controls__fZ_NF {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.ClassProjects_controlItem__8imex {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ClassProjects_controlLabel__QKYtz {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.ClassProjects_classSelect__KtatC,
.ClassProjects_orderSelect__RGSAP {
  width: 100%;
}

.ClassProjects_searchInput__4whbw {
  width: 100%;
}

/* 内容区域 */
.ClassProjects_content__RE4IF {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 400px;
}

/* 加载状态 */
.ClassProjects_loadingContainer__JGevc {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  gap: 16px;
}

.ClassProjects_loadingText__2eN3_ {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* 空状态 */
.ClassProjects_emptyContainer__cgmSj {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}

.ClassProjects_emptyDescription__gGQon {
  text-align: center;
}

.ClassProjects_emptyDescription__gGQon p {
  margin: 0;
  color: #6b7280;
}

.ClassProjects_emptyHint__o_e12 {
  font-size: 12px !important;
  margin-top: 8px !important;
}

/* 项目网格 */
.ClassProjects_projectGrid__xofvU {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  padding: 24px;
}

/* 项目卡片 */
.ClassProjects_projectCard__dlW3R {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.ClassProjects_projectCard__dlW3R:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.ClassProjects_projectCover__VWtC0 {
  position: relative;
  overflow: hidden;
  height: 200px;
}

.ClassProjects_projectImage__1swcu {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.ClassProjects_projectCard__dlW3R:hover .ClassProjects_projectImage__1swcu {
  transform: scale(1.05);
}

.ClassProjects_projectOverlay__Wf3Zg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ClassProjects_projectCard__dlW3R:hover .ClassProjects_projectOverlay__Wf3Zg {
  opacity: 1;
}

.ClassProjects_viewButton__jJ62P {
  border-radius: 8px;
  font-weight: 500;
}

/* 项目信息 */
.ClassProjects_projectInfo__iYbng {
  padding: 16px;
}

.ClassProjects_projectTitle__hBncv {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.ClassProjects_projectDescription__YVHYX {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 项目元信息 */
.ClassProjects_projectMeta__Oyxqp {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.ClassProjects_authorInfo__Dvxo9 {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ClassProjects_authorAvatar__T9q98 {
  flex-shrink: 0;
}

.ClassProjects_authorName__uHNtE {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.ClassProjects_classInfo__Yjc1C {
  display: flex;
  align-items: center;
}

.ClassProjects_classTag__j2H0E {
  margin: 0;
  font-size: 12px;
  border-radius: 6px;
}

/* 项目底部 */
.ClassProjects_projectFooter__j9rMc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f3f4f6;
}

.ClassProjects_publishTime__aHc6g,
.ClassProjects_viewCount__QZW15 {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ClassProjects_container__eRZ_8 {
    padding: 16px;
  }
  
  .ClassProjects_controls__fZ_NF {
    padding: 16px;
  }
  
  .ClassProjects_projectGrid__xofvU {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }
  
  .ClassProjects_title__CJENO {
    font-size: 20px;
  }
  
  .ClassProjects_projectMeta__Oyxqp {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .ClassProjects_container__eRZ_8 {
    padding: 12px;
  }
  
  .ClassProjects_controls__fZ_NF {
    padding: 12px;
  }
  
  .ClassProjects_projectGrid__xofvU {
    padding: 12px;
  }
  
  .ClassProjects_projectInfo__iYbng {
    padding: 12px;
  }
}

/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./node_modules/@icon-park/react/styles/index.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
.i-icon{display:inline-block;color:inherit;font-style:normal;line-height:0;text-align:center;text-transform:none;vertical-align:-.125em;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.i-icon-spin svg{animation:i-icon-spin 1s infinite linear}.i-icon-rtl{transform:scaleX(-1)}@keyframes i-icon-spin{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes i-icon-spin{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}
/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./app/workbench/components/RightSidebar.module.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
/* 右侧边栏样式 */
.RightSidebar_rightSidebar__5ihMQ {
  background: white;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  border-left: 1px solid #eef0f2;
  width: 300px;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

/* 收缩状态时的宽度 */
.RightSidebar_rightSidebar__5ihMQ.RightSidebar_collapsed__BR2Fs {
  width: 60px;
  min-width: 60px;
}

/* 收缩状态的侧边栏 */
.RightSidebar_collapsedSidebar__FEXj5 {
  width: 100%;
  height: 100vh;
  background: white;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 20px;
}

/* 展开按钮 */
.RightSidebar_expandBtn__FygMm {
  background: #64748b;
  border: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(100, 116, 139, 0.2);
}

.RightSidebar_expandBtn__FygMm:hover {
  background: #475569;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(100, 116, 139, 0.3);
}

/* 收缩按钮 */
.RightSidebar_collapseBtn__mRPd4 {
  background: #64748b;
  border: none;
  border-radius: 6px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #ffffff;
  box-shadow: 0 1px 3px rgba(100, 116, 139, 0.2);
}

.RightSidebar_collapseBtn__mRPd4:hover {
  background: #475569;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(100, 116, 139, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .RightSidebar_rightSidebar__5ihMQ {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .RightSidebar_rightSidebar__5ihMQ {
    width: 100%;
    border-left: none;
    border-top: 1px solid #eef0f2;
  }
  
  .RightSidebar_collapsedSidebar__FEXj5 {
    width: 100%;
    height: 60px;
    flex-direction: row;
    padding: 0 20px;
    align-items: center;
    justify-content: flex-end;
  }
}

/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/workbench/styles/workbench.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/* 全局输入框样式重置 */
.search-bar input,
.search-bar input:focus,
.search-bar input:active,
.search-bar input:focus-visible {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    outline-offset: 0 !important;
    outline-width: 0 !important;
    outline-style: none !important;
    outline-color: transparent !important;
}

/* 整体布局 */
.workbench-container {
    display: flex;
    min-height: 100vh;
    height: 100vh;
    background-color: #f7f8fa;
    color: #333;
    overflow: hidden;
    position: relative;
    max-width: 100vw;
}

.left-sidebar {
    width: 240px;
    background-color: #fff;
    border-right: 1px solid #eef0f2;
    display: flex;
    flex-direction: column;
    padding: 20px 12px;
    overflow-y: auto;
    flex-shrink: 0;
}

.main-content-area {
    flex: 1;
    min-width: 0;
    overflow: hidden; /* 移除滚动，让内层容器处理滚动 */
    position: relative;
    background-image: linear-gradient(rgba(0,0,0,0.02) 1px, transparent 1px), linear-gradient(90deg, rgba(0,0,0,0.02) 1px, transparent 1px);
    background-size: 20px 20px;
    padding: 0; /* 移除默认内边距，使用内层容器的内边距 */
}

/* 右侧抽屉样式 */
.workbench-container aside:not(.left-sidebar) {
    width: 300px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

/* 右侧边栏收缩状态 */
.workbench-container aside:not(.left-sidebar).collapsed {
    width: 60px;
    min-width: 60px;
}

/* 左侧导航栏 */
.left-sidebar .sidebar-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 12px;
    margin-bottom: 24px;
    font-size: 16px;
    font-weight: 600;
}

.left-sidebar .teacher-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
    padding: 0 12px;
}
.left-sidebar .teacher-info .avatar {
    border-radius: 50%;
}
.left-sidebar .teacher-name {
    font-weight: 600;
}
.left-sidebar .teacher-title {
    font-size: 12px;
    color: #909399;
}

/* 登录提示区域样式 */
.left-sidebar .login-prompt {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
    padding: 0 12px;
}
.left-sidebar .login-icon-container {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 50%;
}
.left-sidebar .login-details {
    flex: 1;
}
.left-sidebar .login-text {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #909399;
}
.left-sidebar .login-subtitle {
    margin: 0;
    font-size: 12px;
    color: #c0c4cc;
    margin-top: 2px;
}
.left-sidebar .sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
}
.left-sidebar .nav-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s, color 0.2s;
    position: relative;
    color: #606266;
}
.left-sidebar .nav-item:hover {
    background-color: #f5f7fa;
}
.left-sidebar .nav-item.active {
    background-color: #f0f4ff;
    color: #4a6fff;
    font-weight: 600;
}

.left-sidebar .nav-item.active .nav-icon {
    color: #4a6fff;
}
.left-sidebar .nav-icon {
    margin-right: 12px;
    width: 20px;
    height: 20px;
}
.left-sidebar .sidebar-footer {
    margin-top: auto;
    display: flex;
    align-items: center;
    padding: 12px;
    cursor: pointer;
    color: #606266;
    border-radius: 8px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}
.left-sidebar .sidebar-footer:hover {
    background-color: #f0f4ff;
    color: #4a6fff;
    border-color: #e4e7ed;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(74, 111, 255, 0.15);
}
.left-sidebar .sidebar-footer:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(74, 111, 255, 0.1);
}

/* Add animation to nav-item transitions */
.left-sidebar .nav-item::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%) scaleY(0);
    height: 60%;
    width: 4px;
    background-color: #4a6fff;
    border-radius: 0 4px 4px 0;
    transition: transform 0.3s ease, height 0.3s ease;
}

.left-sidebar .nav-item.active::before {
    transform: translateY(-50%) scaleY(1);
}

/* 导航分隔线 */
.left-sidebar .nav-divider {
    height: 1px;
    background-color: #eef0f2;
    margin: 8px 0;
    width: 100%;
}

/* 下拉菜单样式 */
.left-sidebar .nav-item-dropdown {
    position: relative;
}

.left-sidebar .nav-item.dropdown-open {
    background-color: #f0f4ff;
    color: #4a6fff;
}

/* 只有当菜单项既是active又是dropdown-open时才显示蓝色竖条 */
.left-sidebar .nav-item.active.dropdown-open::before {
    transform: translateY(-50%) scaleY(1);
}

.left-sidebar .dropdown-arrow {
    margin-left: auto;
    transition: transform 0.3s ease;
    color: #909399;
    flex-shrink: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.left-sidebar .dropdown-arrow.rotated {
    transform: rotate(180deg) !important;
    color: #4a6fff;
}

.left-sidebar .nav-item.dropdown-open .dropdown-arrow {
    color: #4a6fff;
}

.left-sidebar .dropdown-menu {
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    margin-top: 4px;
    margin-left: 0;
    padding: 4px 12px;
    border: none;
    animation: dropdownSlide 0.2s ease-out;
    width: 100%;
    max-height: 280px;
    overflow-y: auto;
    position: relative;
    z-index: 10;
}

/* 优化滚动条样式 */
.left-sidebar .dropdown-menu::-webkit-scrollbar {
    width: 4px;
}

.left-sidebar .dropdown-menu::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
}

.left-sidebar .dropdown-menu::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
    transition: background 0.2s ease;
}

.left-sidebar .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

@keyframes dropdownSlide {
    from {
        opacity: 0;
        transform: translateY(-8px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.left-sidebar .dropdown-item {
    padding: 10px 16px 10px 40px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #606266;
    font-size: 14px;
    font-weight: 400;
    background: transparent;
    border: none;
    text-align: left;
    width: 100%;
    margin: 2px 0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    min-height: 40px;
    position: relative;
}

.left-sidebar .dropdown-item:hover:not(.disabled) {
    background-color: #f5f7fa;
    color: #303133;
    transform: none;
}

.left-sidebar .dropdown-item.selected {
    background-color: #ecf5ff;
    color: #4a6fff;
    font-weight: 500;
    position: relative;
    border: none;
}



.left-sidebar .dropdown-item.selected::after {
    display: none;
}

.left-sidebar .dropdown-item .school-info {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 2px;
}

.left-sidebar .dropdown-item .school-name {
    font-weight: 500;
    color: inherit;
    font-size: 14px;
    line-height: 1.3;
}

.left-sidebar .dropdown-item .school-location {
    display: none; /* 隐藏地理位置信息，保持简洁 */
}

.left-sidebar .dropdown-item.disabled {
    color: #9ca3af;
    cursor: not-allowed;
    background: #f9fafb;
    text-align: center;
    font-style: normal;
    position: relative;
    padding: 16px;
    border: 1px dashed #d1d5db;
    margin: 6px;
    border-radius: 6px;
    font-size: 13px;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.left-sidebar .dropdown-item.disabled::before {
    content: '🏫';
    display: block;
    font-size: 28px;
    margin-bottom: 8px;
    opacity: 0.6;
    filter: grayscale(0.3);
}

.left-sidebar .dropdown-item.disabled::after {
    content: '请联系管理员添加学校信息';
    display: block;
    font-size: 11px;
    color: #6b7280;
    margin-top: 4px;
    font-style: normal;
}

.left-sidebar .dropdown-menu.empty {
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8faff;
    border: none;
}

/* 加载状态样式 */
.left-sidebar .dropdown-item.disabled.loading {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
    border-color: #7dd3fc !important;
    color: #0369a1 !important;
    cursor: not-allowed !important;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 20px 16px;
}

.left-sidebar .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #bae6fd;
    border-top: 2px solid #0369a1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.left-sidebar .dropdown-item.disabled.error {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%) !important;
    border-color: #fca5a5 !important;
    color: #dc2626 !important;
    cursor: not-allowed !important;
}

.left-sidebar .dropdown-item.disabled.error::before {
    content: '⚠️';
    display: block;
    font-size: 20px;
    margin-bottom: 4px;
}

/* 无数据状态样式 */
.left-sidebar .dropdown-item.disabled.no-data {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border-color: #cbd5e1 !important;
    color: #64748b !important;
    cursor: not-allowed !important;
}

.left-sidebar .dropdown-item.disabled.no-data::before {
    content: '📂';
    display: block;
    font-size: 20px;
    margin-bottom: 4px;
    opacity: 0.6;
}

/* 确保禁用状态的菜单项不会有悬停效果 */
.left-sidebar .dropdown-item.disabled:hover {
    cursor: not-allowed !important;
    background: inherit !important;
    color: inherit !important;
    transform: none !important;
}

.left-sidebar .dropdown-item.disabled.loading:hover {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
    color: #0369a1 !important;
    cursor: not-allowed !important;
}

.left-sidebar .dropdown-item.disabled.error:hover {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%) !important;
    color: #dc2626 !important;
    cursor: not-allowed !important;
}

.left-sidebar .dropdown-item.disabled.no-data:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    color: #64748b !important;
    cursor: not-allowed !important;
}

/* 主内容区 */
.main-content {
    flex-direction: column;
    width: 100%;
    min-width: 0;
    position: relative;
    display: flex; /* 确保flex布局 */
    height: 100%;
    overflow: hidden;
}

/* 内容区域容器 */
.content-area {
    padding: 24px;
    height: calc(100vh - 80px);
}

/* 全屏内容区域容器 - 用于班级任务等页面 */
.content-area-fullscreen {
    padding: 0;
    height: 100vh;
    width: 100%;
}

.main-content .main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    height: 80px;
    padding:20px;
    background: white;
    border-radius: 0;
    border: none;
    border-bottom:1px solid #eef0f2;
    gap:20px;
}
.main-content .search-bar {
    display: flex;
    flex:1;
    align-items: center;
    background: #f8fafc;
    padding: 12px 20px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
    position: relative;
}

.main-content .search-bar:hover {
    border-color: #c7d2fe;
    background: white;
}

.main-content .search-bar:focus-within {
    border-color: #4a6fff;
    background: white;
    box-shadow: 0 0 0 3px rgba(74, 111, 255, 0.1);
}

.main-content .search-icon {
    color: #6b7280;
    margin-right: 12px;
    transition: all 0.2s ease;
}

.main-content .search-bar:focus-within .search-icon {
    color: #4a6fff;
    color: #2563eb;
    transform: scale(1.1);
}

.main-content .search-bar input {
    border: none !important;
    outline: none !important;
    background: transparent;
    width: 100%;
    font-size: 14px;
    color: #6b7280;
    font-weight: 400;
    z-index: 1;
    position: relative;
    box-shadow: none !important;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
}

.main-content .search-bar input:focus,
.main-content .search-bar input:active,
.main-content .search-bar input:focus-visible {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    outline-offset: 0 !important;
    outline-width: 0 !important;
    outline-style: none !important;
    outline-color: transparent !important;
}

.main-content .search-bar input::placeholder {
    color: #9ca3af;
    font-weight: 400;
    transition: color 0.3s ease;
}

.main-content .search-bar:focus-within input::placeholder {
    color: #6b7280;
}

.main-content .start-class-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: #4a6fff;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 8px rgba(74, 111, 255, 0.2);
    white-space: nowrap;
}

.main-content .start-class-btn:hover {
    background-color: #3b52cc;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 111, 255, 0.3);
}

.main-content .start-class-btn .start-class-icon {
    width: 20px;
    height: 20px;
    fill: white;
    flex-shrink: 0;
}

.main-content .start-class-btn span {
    flex-shrink: 0;
}

/* 头部标题样式 */
.main-content .header-title {
    flex: 1;
    display: flex;
    align-items: center;
}

.main-content .header-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

/* 发布任务按钮样式 */
.main-content .publish-task-btn {
    background: #10b981;
    color: white;
}

.main-content .publish-task-btn:hover {
    background: #059669;
    transform: translateY(-1px);
}

.main-content .publish-task-btn:active {
    background: #047857;
    transform: translateY(1px);
}

.section-title {
    font-size: 20px;
    font-weight: 700;
    /* margin-bottom: 20px; */
    color: #303133;
    text-align: left;
    padding-left: 0;
    border-bottom: none;
    text-decoration: none;
    position: relative;
}

.section-title::before,
.section-title::after {
    display: none;
}

/* 快速操作 */
.quick-actions .section-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #303133;
    text-align: left;
    padding-left: 0;
    border-bottom: none;
    text-decoration: none;
}

.quick-actions .quick-actions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.quick-actions {
    width: 100%;
    min-width: 0;
}

.quick-actions .actions-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 16px;
    width: 100%;
    min-width: 0;
}
.action-card {
    padding: 24px;
    border-radius: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    min-width: 0;
    width: 100%;
}
.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.08);
}
.action-card:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.06);
}
.action-card.green { background: linear-gradient(135deg, #f0fff0, #d4f4d6); }
.action-card.purple { background: linear-gradient(135deg, #f5f0ff, #e2d9ff); }
.action-card.orange { background: linear-gradient(135deg, #fff5e6, #ffe2d1); }
.action-card h3 { font-size: 16px; font-weight: 600; margin-bottom: 8px; }
.action-card p { font-size: 13px; color: #606266; }
.action-card .card-icon {
    background-color: rgba(0, 0, 0, 0.04);
    border-radius: 50%;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 进行中的任务 */
.ongoing-tasks .section-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #303133;
    text-align: left;
    padding-left: 0;
    border-bottom: none;
    text-decoration: none;
}

.ongoing-tasks {
    width: 100%;
    min-width: 0;
}

.ongoing-tasks .filter-tabs-container {
    background: #f0f8ff;
    border-radius: 12px;
    padding: 12px 20px;
    border: 1px solid #e6f3ff;
    margin-bottom: 20px;
    width: 100%;
    min-width: 1000px;
    overflow: visible;
}

.ongoing-tasks .filter-tabs {
    position: relative;
    height: 40px;
    padding: 0 20px;
    width: 100%;
    min-width: 1000px;
}

.ongoing-tasks .filter-tab {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 14px;
    color: #606266;
    cursor: pointer;
    padding: 8px 0;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
    text-align: center;
    position: absolute;
}

.ongoing-tasks .filter-tab:nth-child(1) {
    left: 76px;
    width: 60px;
}

.ongoing-tasks .filter-tab:nth-child(2) {
    left: 220px;
    width: 120px;
}

.ongoing-tasks .filter-tab:nth-child(3) {
    left: 450px;
    width: 80px;
}

.ongoing-tasks .filter-tab:nth-child(4) {
    left: 580px;
    width: 60px;
}

.ongoing-tasks .filter-tab:nth-child(5) {
    left: 690px;
    width: 80px;
}

.ongoing-tasks .filter-tab:nth-child(6) {
    left: 780px;
    width: 100px;
}

.ongoing-tasks .filter-tab:hover {
    color: #0080ff;
    border-bottom-color: #0080ff;
}

.ongoing-tasks .tasks-grid {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
    min-width: 0;
}

.ongoing-tasks .task-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    transition: all 0.2s;
    width: 100%;
    min-width: 0;
    overflow: hidden;
}

.ongoing-tasks .task-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.ongoing-tasks .task-card-content {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 100%;
    padding: 0 20px;
}

.ongoing-tasks .task-left {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
    min-width: 0;
    justify-content: center;
    padding-left: 30px;
}

.ongoing-tasks .progress-circle-modern {
    width: 52px;
    height: 52px;
    border-radius: 50%;
    background: conic-gradient(#0080ff 0deg 288deg, #f0f0f0 288deg 360deg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    flex-shrink: 0;
}

.ongoing-tasks .progress-circle-modern::before {
    content: '';
    position: absolute;
    width: 38px;
    height: 38px;
    background: white;
    border-radius: 50%;
}

.ongoing-tasks .progress-text {
    position: relative;
    z-index: 2;
    font-weight: 600;
    font-size: 12px;
    color: #0080ff;
}

.ongoing-tasks .task-details {
    flex: 1;
    text-align: center;
}

.ongoing-tasks .task-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    line-height: 1.5;
    letter-spacing: -0.01em;
}

.ongoing-tasks .task-description {
    font-size: 13px;
    color: #6b7280;
    line-height: 1.4;
    margin-top: 4px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.ongoing-tasks .task-right {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: space-evenly;
    min-width: 0;
}

.ongoing-tasks .task-stats {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    width: 100%;
    gap: 0;
}

.ongoing-tasks .stat-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 14px;
    color: #4b5563;
    flex: 1;
    text-align: center;
}

.ongoing-tasks .stat-value {
    font-weight: 600;
    color: #1f2937;
    font-size: 15px;
}

.ongoing-tasks .star-icon {
    color: #fbbf24;
}

.ongoing-tasks .class-info {
    flex: 1;
    display: flex;
    justify-content: center;
}

.ongoing-tasks .class-badge {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 10px 16px;
    text-align: center;
    min-width: 100px;
    max-width: 120px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ongoing-tasks .class-name {
    font-size: 13px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 2px;
}

.ongoing-tasks .subclass-name {
    font-size: 12px;
    color: #909399;
}

.user-profile-card {
    background-color: #fff;
    border-radius: 16px;
    padding: 20px;
    text-align: center;
    border: 1px solid #eef0f2;
}
.user-profile-card .profile-avatar-container {
    margin: -65px auto 10px;
    width: 88px;
    height: 88px;
    border-radius: 50%;
    position: relative;
}
.user-profile-card .profile-avatar-container::before {
    content: '';
    position: absolute;
    inset: -6px;
    background: conic-gradient(from 90deg, #3ddc84, #a767ff, #ff8c5a, #3ddc84);
    border-radius: 50%;
    z-index: 0;
    filter: blur(4px);
    opacity: 0.8;
}
.user-profile-card .profile-avatar {
    position: relative;
    z-index: 1;
    border-radius: 50%;
    border: 4px solid #fff;
}
.user-profile-card .profile-name {
    font-size: 18px;
    font-weight: 600;
}
.user-profile-card .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin: 20px 0;
}
.user-profile-card .stat-value {
    font-size: 20px;
    font-weight: bold;
}
.user-profile-card .stat-label {
    font-size: 13px;
    color: #909399;
}
.user-profile-card .energy-balance {
    margin-top: 20px;
    min-height: 50px;
    align-items: flex-start;
}
.user-profile-card .energy-value {
    font-size: 22px;
    font-weight: bold;
    color: #4a6fff;
    margin: 0;
    line-height: 1.2;
}
.user-profile-card .recharge-btn {
    width: 100%;
    background-color: #4a6fff;
    color: #fff;
    border: none;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}
.user-profile-card .recharge-btn:hover {
    background-color: #5a7fff;
}

.my-courses-card {
    background-color: #fff;
    border-radius: 16px;
    padding: 20px;
    border: 1px solid #eef0f2;
}
.my-courses-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}
.my-courses-card h4 { font-weight: 600; font-size: 16px; }
.my-courses-card .courses-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
}
.my-courses-card .course-item {
    display: flex;
    align-items: center;
    gap: 12px;
}
.my-courses-card .course-icon-placeholder {
    width: 48px;
    height: 48px;
    background-color: #f0f4ff;
    border-radius: 8px;
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='8' ry='8' stroke='%23D4DFFF' stroke-width='2' stroke-dasharray='6%2c 6' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
}
.my-courses-card .course-name {
    font-weight: 500;
}
.my-courses-card .course-series {
    font-size: 13px;
    color: #909399;
}
.my-courses-card .draft-btn {
    margin-left: auto;
    background: #f5f7fa;
    border: 1px solid #eef0f2;
    color: #606266;
    border-radius: 12px;
    padding: 4px 10px;
    font-size: 12px;
    cursor: pointer;
}

.current-template-card {
    background-color: #fff;
    border-radius: 16px;
    padding: 20px;
    border: 1px solid #eef0f2;
    font-size: 14px;
}
.current-template-card .template-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-weight: 500;
}
.current-template-card .template-info > svg {
    color: #909399;
}



/* 移除响应式布局 - 1200px断点 */

/* 移除响应式布局 - 992px断点 */

/* 移除响应式布局 - 768px断点 */

/* 移除响应式布局 - 480px断点 */

/* 移除响应式布局 - 1400px断点 */

/* 模板管理样式 */
.template-management {
    padding: 20px 24px;
    height: 100%;
    overflow-y: auto;
    background: #f8fafc;
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0px;
}

.search-container {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-container .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #9CA3AF;
}

.search-input {
    width: 100%;
    border: 1px solid #E2E8F0;
    font-size: 14px;
    outline: none;
    transition: all 0.2s ease;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
    color: #94A3B8;
}

.upload-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
}

.upload-btn:hover {
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35);
    transform: translateY(-1px);
}

.template-tabs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    margin-top: 8px;
}

.template-tabs-group {
    display: flex;
    align-items: center;
    gap: 0;
    background: #E2E8F0;
    border-radius: 40px;
    padding: 4px;
    width: fit-content;
    position: relative;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.template-tabs-group::before {
    content: '';
    position: absolute;
    top: 4px;
    left: 4px;
    width: calc(50% - 4px);
    height: calc(100% - 8px);
    background: linear-gradient(135deg, #F97316 0%, #EA580C 100%);
    border-radius: 32px;
    transition: transform 0.3s ease;
    z-index: 1;
    box-shadow:
        0 2px 8px rgba(249, 115, 22, 0.3),
        0 1px 3px rgba(249, 115, 22, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.template-tabs-group.official-active::before {
    transform: translateX(100%);
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    box-shadow:
        0 2px 8px rgba(37, 99, 235, 0.3),
        0 1px 3px rgba(37, 99, 235, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tab-btn {
    padding: 14px 28px;
    border: none;
    background: transparent;
    color: #94A3B8;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 150px;
    justify-content: center;
    border-radius: 32px;
    z-index: 2;
}

.tab-btn:first-child {
    border-radius: 32px;
}

.tab-btn:nth-child(2) {
    border-radius: 32px;
    margin-left: 0;
}

.tab-btn.active {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 当官方模板被选中时（滑动按钮在左侧显示橙色），官方模板按钮文字变白色 */
.template-tabs-group:not(.official-active) .tab-btn:first-child {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.tab-btn:hover:not(.active) {
    color: #64748B;
}

.tab-btn::before {
    content: '';
    width: 20px;
    height: 20px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    flex-shrink: 0;
}

.tab-btn:first-child::before {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 6H8C6.89543 6 6 6.89543 6 8V18C6 19.1046 6.89543 20 8 20H18C19.1046 20 20 19.1046 20 18V8C20 6.89543 19.1046 6 18 6Z' fill='none' stroke='%2394A3B8' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M18 28H8C6.89543 28 6 28.8954 6 30V40C6 41.1046 6.89543 42 8 42H18C19.1046 42 20 41.1046 20 40V30C20 28.8954 19.1046 28 18 28Z' fill='none' stroke='%2394A3B8' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M35 20C38.866 20 42 16.866 42 13C42 9.13401 38.866 6 35 6C31.134 6 28 9.13401 28 13C28 16.866 31.134 20 35 20Z' fill='none' stroke='%2394A3B8' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M40 28H30C28.8954 28 28 28.8954 28 30V40C28 41.1046 28.8954 42 30 42H40C41.1046 42 42 41.1046 42 40V30C42 28.8954 41.1046 28 40 28Z' fill='none' stroke='%2394A3B8' stroke-width='4' stroke-linejoin='round'/%3E%3C/svg%3E");
}

/* 当官方模板被选中时（滑动按钮在左侧显示橙色），官方模板按钮图标变白色 */
.template-tabs-group:not(.official-active) .tab-btn:first-child::before {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 6H8C6.89543 6 6 6.89543 6 8V18C6 19.1046 6.89543 20 8 20H18C19.1046 20 20 19.1046 20 18V8C20 6.89543 19.1046 6 18 6Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M18 28H8C6.89543 28 6 28.8954 6 30V40C6 41.1046 6.89543 42 8 42H18C19.1046 42 20 41.1046 20 40V30C20 28.8954 19.1046 28 18 28Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M35 20C38.866 20 42 16.866 42 13C42 9.13401 38.866 6 35 6C31.134 6 28 9.13401 28 13C28 16.866 31.134 20 35 20Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M40 28H30C28.8954 28 28 28.8954 28 30V40C28 41.1046 28.8954 42 30 42H40C41.1046 42 42 41.1046 42 40V30C42 28.8954 41.1046 28 40 28Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.tab-btn:first-child.active::before {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 6H8C6.89543 6 6 6.89543 6 8V18C6 19.1046 6.89543 20 8 20H18C19.1046 20 20 19.1046 20 18V8C20 6.89543 19.1046 6 18 6Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M18 28H8C6.89543 28 6 28.8954 6 30V40C6 41.1046 6.89543 42 8 42H18C19.1046 42 20 41.1046 20 40V30C20 28.8954 19.1046 28 18 28Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M35 20C38.866 20 42 16.866 42 13C42 9.13401 38.866 6 35 6C31.134 6 28 9.13401 28 13C28 16.866 31.134 20 35 20Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M40 28H30C28.8954 28 28 28.8954 28 30V40C28 41.1046 28.8954 42 30 42H40C41.1046 42 42 41.1046 42 40V30C42 28.8954 41.1046 28 40 28Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.tab-btn:nth-child(2)::before {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4 11.9143L24 19L44 11.9143L24 5L4 11.9143Z' fill='none' stroke='%2394A3B8' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M4 20L24 27L44 20' stroke='%2394A3B8' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M4 28L24 35L44 28' stroke='%2394A3B8' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M4 36L24 43L44 36' stroke='%2394A3B8' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.tab-btn:nth-child(2).active::before {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4 11.9143L24 19L44 11.9143L24 5L4 11.9143Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M4 20L24 27L44 20' stroke='white' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M4 28L24 35L44 28' stroke='white' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M4 36L24 43L44 36' stroke='white' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.create-template-btn {
    padding: 12px 24px;
    background: #3B82F6;
    color: white;
    border: none;
    border-radius: 30px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
    opacity: 1;
    transform: translateX(0);
    animation: slideInFromRight 0.3s ease-out;
}

.create-template-btn:hover {
    background: #2563EB;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35);
    transform: translateY(-1px);
}

/* 创建模板按钮的进入动画 */
@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.create-template-icon {
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3B82F6;
    flex-shrink: 0;
}

.template-grid {
    display: grid;
    /* 默认单列布局，用于官方模板 */
    grid-template-columns: 1fr;
    gap: 16px;
    max-width: 100%;
}

/* 我的模板使用两列布局 */
.template-grid.my-templates {
    grid-template-columns: repeat(2, 1fr);
}

/* 模板加载状态 */
.template-grid .loading-placeholder {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    background: #f8fafc;
    border-radius: 12px;
    border: 2px dashed #e2e8f0;
}

.template-grid .loading-placeholder .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #E5E7EB;
    border-top: 3px solid #3B82F6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

.template-grid .loading-placeholder p {
    color: #6B7280;
    font-size: 14px;
    margin: 0;
}

/* 模板空状态 */
.template-grid .empty-placeholder {
    grid-column: 1 / -1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    background: #f8fafc;
    border-radius: 12px;
    border: 2px dashed #e2e8f0;
}

.template-grid .empty-placeholder p {
    color: #6B7280;
    font-size: 16px;
    margin: 0;
}

.template-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    border: 1px solid #E2E8F0;
    border-radius: 16px;
    transition: all 0.3s ease;
    min-height: 88px;
}

.template-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #CBD5E1;
    transform: translateY(-2px);
}

/* 可编辑模板卡片样式 */
.template-card.editable {
    position: relative;
}

.template-card.editable:hover {
    border-color: #3B82F6;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.template-card.editable:hover .template-name {
    color: #3B82F6;
}

.template-card.editable:hover .edit-hint {
    opacity: 1;
    transform: translateY(0);
}

.template-icon {
    width: 52px;
    height: 52px;
    background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}

/* 我的模板（自定义模板）的图标使用蓝色 */
.template-card.editable .template-icon {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
}

.icon-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3px;
}

.icon-dot {
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 1px;
}

.template-info {
    flex: 1;
    min-width: 0;
}

.template-title {
    margin-bottom: 4px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.template-name-with-badges {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.current-template-badge {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    color: #0369a1;
    padding: 3px 10px;
    border-radius: 14px;
    font-size: 11px;
    font-weight: 600;
    white-space: nowrap;
    border: 1px solid #7dd3fc;
    box-shadow: 0 1px 3px rgba(3, 105, 161, 0.1);
    position: relative;
    overflow: hidden;
}

.current-template-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.current-template-badge:hover::before {
    left: 100%;
}

.template-type {
    font-size: 12px;
    font-weight: 500;
    color: #6366F1;
    letter-spacing: 0;
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid rgba(99, 102, 241, 0.3);
    border-radius: 12px;
    padding: 4px 8px;
    display: inline-block;
    line-height: 1;
    transition: all 0.2s ease;
}

/* 编辑提示样式 */
.edit-hint {
    margin-top: 4px;
    opacity: 0;
    transform: translateY(-5px);
    transition: all 0.3s ease;
}

.edit-hint span {
    font-size: 12px;
    color: #3B82F6;
    font-weight: 500;
}

/* 官方模板样式 */
.template-card .template-type.official {
    color: #059669;
    background: rgba(5, 150, 105, 0.1);
    border: 1px solid rgba(5, 150, 105, 0.3);
}

.template-type:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.template-name {
    font-size: 14px;
    color: #64748B;
    font-weight: 500;
}

.template-meta {
    font-size: 13px;
    color: #94A3B8;
    margin-top: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* 当 template-meta 在 template-title 内部时的样式调整 */
.template-title .template-meta {
    margin-top: 4px;
}

.template-description {
    font-size: 12px;
    color: #64748B;
    line-height: 1.4;
    max-height: 2.8em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.template-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
}

.usage-info {
    font-size: 12px;
    color: #64748B;
    text-align: right;
    margin-bottom: 4px;
}

.use-template-btn {
    padding: 8px 18px;
    background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
    color: #F97316;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(251, 191, 36, 0.2);
}

/* 我的模板（自定义模板）的按钮使用蓝色 */
.template-card.editable .use-template-btn {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
    color: white;
}

.use-template-btn:hover {
    background: linear-gradient(135deg, #FDE68A 0%, #FBBF24 100%);
    box-shadow: 0 4px 8px rgba(251, 191, 36, 0.3);
    transform: translateY(-1px);
}

/* 我的模板（自定义模板）按钮的悬停效果使用蓝色 */
.template-card.editable .use-template-btn:hover {
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.use-template-btn.current,
.template-card.editable .use-template-btn.current,
.template-card:not(.editable) .use-template-btn.current {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
    color: #0369a1 !important;
    border: 1px solid #7dd3fc !important;
    cursor: default;
    box-shadow: 0 2px 4px rgba(3, 105, 161, 0.1) !important;
    position: relative;
    overflow: hidden;
    font-weight: 600;
}

.use-template-btn.current::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(125, 211, 252, 0.1) 0%, rgba(224, 242, 254, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.use-template-btn.current:hover,
.template-card.editable .use-template-btn.current:hover,
.template-card:not(.editable) .use-template-btn.current:hover {
    background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%) !important;
    color: #0369a1 !important;
    transform: none !important;
    box-shadow: 0 3px 6px rgba(3, 105, 161, 0.15) !important;
    border-color: #38bdf8 !important;
}

.use-template-btn.current:hover::before {
    opacity: 1;
}

/* 添加一个小图标效果 */
.use-template-btn.current::after {
    content: '✓';
    margin-left: 6px;
    font-size: 12px;
    font-weight: bold;
}

/* 班级管理样式 - 已移至 ClassManagement.css */
/* .class-management-container {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.class-management-content {
    text-align: left;
}

.class-management-content h2 {
    font-size: 24px;
    color: #1F2937;
    margin-bottom: 16px;
}

.class-management-content p {
    font-size: 16px;
    color: #6B7280;
} */

/* 班级任务样式 */
.class-tasks-container {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
}

.class-tasks-content {
    text-align: center;
}

.class-tasks-content h2 {
    font-size: 24px;
    color: #1F2937;
    margin-bottom: 16px;
}

.class-tasks-content p {
    font-size: 16px;
    color: #6B7280;
}

/* 课程管理样式 */
.course-management-container {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.course-management-content {
    text-align: center;
}

.course-management-content h2 {
    font-size: 24px;
    color: #1F2937;
    margin-bottom: 16px;
}

.course-management-content p {
    font-size: 16px;
    color: #6B7280;
}

/* 班级项目样式 */
.class-projects-container {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.class-projects-content {
    text-align: center;
}

.class-projects-content h2 {
    font-size: 24px;
    color: #1F2937;
    margin-bottom: 16px;
}

.class-projects-content p {
    font-size: 16px;
    color: #6B7280;
}

/* 弹窗加载和错误状态样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    padding: 0 20px 20px 20px;
    text-align: left;
    width: 100%;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #E5E7EB;
    border-top: 3px solid #3B82F6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-container {
    display: flex;
    flex-direction: column;
    padding: 0 20px 20px 20px;
    text-align: left;
    width: 100%;
}

.error-message {
    color: #EF4444;
    margin-bottom: 16px;
    font-size: 14px;
}

.retry-btn {
    padding: 8px 16px;
    background: #3B82F6;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.retry-btn:hover {
    background: #2563EB;
}

.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.empty-hint {
    color: #9CA3AF;
    font-size: 12px;
    margin-top: 8px;
}

.school-card-location {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #6B7280;
    font-size: 12px;
    margin-top: 8px;
}

.class-card-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    color: #6B7280;
    font-size: 12px;
    margin-top: 8px;
}



/* 加载占位符样式 */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    width: 100%;
    background: #f8fafc;
}

.loading-placeholder .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #E5E7EB;
    border-top: 4px solid #3B82F6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

.loading-placeholder p {
    color: #6B7280;
    font-size: 16px;
    margin: 0;
}

/* 班级管理页面样式 - 已移至 ClassManagement.css */
/* .class-management-container {
    padding: 0;
    background: #f8faff;
    min-height: 100vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}

.class-management-content {
    max-width: none;
    margin: 0;
    padding: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}

.class-management-header {
    margin: 0 0 20px 0;
    padding: 20px 0 0 20px;
    text-align: left;
    width: 100%;
}

.class-management-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    text-align: left;
    line-height: 1.2;
    position: relative;
}

.class-management-header h2::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 48px;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
    border-radius: 2px;
} */

.school-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.school-name {
    font-size: 16px;
    font-weight: 500;
    color: #4a90e2;
}

.school-location {
    font-size: 14px;
    color: #9ca3af;
}

/* 班级网格布局 - 已移至 ClassManagement.css */
/* .classes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    margin: 0;
    height: 390px;
    padding:20px 20px 20px 20px;
    justify-content: start;
    align-content: start;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

.classes-grid::-webkit-scrollbar {
    width: 8px;
}

.classes-grid::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    margin: 10px 0;
}

.classes-grid::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.4);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.classes-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.6);
} */

/* 班级卡片样式 - 已移至 ClassManagement.css */
/* .class-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    text-align: center;
    min-height: 80px;
    max-height: 80px;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.class-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
}

.class-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.school-tag {
    background: #f3f4f6;
    color: #6b7280;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.school-tag::before {
    content: "🏫";
    font-size: 14px;
}

.settings-icon {
    color: #9ca3af;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 4px;
    border-radius: 4px;
}

.settings-icon:hover {
    color: #4a90e2;
    background: #f3f4f6;
}

.class-card-content {
    text-align: center;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.class-name {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 16px 0;
}

.student-count-section {
    background: #f1f5f9;
    border-radius: 10px;
    padding: 16px;
    margin-top: 4px;
}

.student-count-number {
    font-size: 32px;
    font-weight: 700;
    color: #3b82f6;
    line-height: 1;
    margin-bottom: 6px;
}

.student-count-label {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
} */

/* 学生卡片操作按钮样式 */
.student-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    margin-left: auto;
    margin-right: 8px;
}

.student-item:hover .student-actions {
    opacity: 1;
    visibility: visible;
}

.action-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.2s ease;
    background: #f1f5f9;
    color: #64748b;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.assign-blocks-btn:hover {
    background: #dbeafe;
    color: #3b82f6;
}

.assign-points-btn:hover {
    background: #fef3c7;
    color: #f59e0b;
}

/* 助教标识和空状态样式 - 已移至 ClassManagement.css */
/* .assistant-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: #fef3c7;
    color: #d97706;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
}

.no-school-selected,
.no-classes {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.no-school-icon,
.no-classes-icon {
    color: #d1d5db;
    margin-bottom: 16px;
}

.no-school-selected h3,
.no-classes h3 {
    font-size: 20px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 8px 0;
}

.no-school-selected p,
.no-classes p {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
} */

/* 响应式设计 - 工作台布局 */
@media (max-width: 1500px) {
    /* 在较小屏幕上，右侧边栏始终保持60px宽度，但不影响浮层 */
    .workbench-container aside:not(.left-sidebar):not(.floating-overlay) {
        width: 60px !important;
        min-width: 60px !important;
        position: relative;
    }

    /* 浮层样式不受限制 */
    .floating-overlay {
        width: 300px !important;
        min-width: 300px !important;
    }

    /* 确保主内容区域占满剩余空间 */
    .main-content-area {
        flex: 1;
        max-width: calc(100% - 60px);
    }
}

@media (max-width: 768px) {
    /* 移动端进一步优化 */
    .left-sidebar {
        width: 200px;
        padding: 16px 8px;
    }

    .workbench-container {
        overflow-x: auto;
    }
}

@media (max-width: 480px) {
    /* 超小屏幕优化 */
    .left-sidebar {
        width: 180px;
        padding: 12px 6px;
    }
}


