'use client';

import React, { useState, useMemo } from 'react';
import { Card, Avatar, Button, Select, Row, Col, Input } from 'antd';
import { Eye, Calendar, BookOpen, Search } from 'lucide-react';
import Image from 'next/image';
import styles from './ClassProjects.module.css';

const { Option } = Select;
const { Search: SearchInput } = Input;

// 演示数据 - 展示多个班级的项目
const demoProjects = [
  {
    id: 1,
    title: '小洛机器人编程大赛作品',
    description: '使用Scratch编程制作的智能机器人，能够自动避障和语音交互，展示了学生们的创新思维和编程能力。',
    coverImage: '/images/xiaoluo-default.webp',
    author: {
      nickName: '张小明',
      avatarUrl: '/images/xiaoluo-default.webp'
    },
    className: '一班',
    grade: '五年级',
    publishToClassTime: '2024-01-15T10:30:00Z',
    viewCount: 128
  },
  {
    id: 2,
    title: '环保主题创意设计',
    description: '关于环境保护的创意海报设计，运用了丰富的色彩和创新的构图，传达了保护地球的重要信息。',
    coverImage: '/images/xiaoluo-default.webp',
    author: {
      nickName: '李小红',
      avatarUrl: '/images/xiaoluo-default.webp'
    },
    className: '二班',
    grade: '四年级',
    publishToClassTime: '2024-01-14T14:20:00Z',
    viewCount: 95
  },
  {
    id: 3,
    title: '数学几何动画演示',
    description: '通过动画的形式展示几何图形的变换过程，帮助同学们更好地理解数学概念，寓教于乐。',
    coverImage: '/images/xiaoluo-default.webp',
    author: {
      nickName: '王小华',
      avatarUrl: '/images/xiaoluo-default.webp'
    },
    className: '三班',
    grade: '六年级',
    publishToClassTime: '2024-01-13T09:15:00Z',
    viewCount: 156
  },
  {
    id: 4,
    title: '科学实验记录视频',
    description: '记录了植物生长过程的延时摄影作品，展现了生命的奇迹和科学观察的重要性。',
    coverImage: '/images/xiaoluo-default.webp',
    author: {
      nickName: '陈小丽',
      avatarUrl: '/images/xiaoluo-default.webp'
    },
    className: '一班',
    grade: '三年级',
    publishToClassTime: '2024-01-12T16:45:00Z',
    viewCount: 203
  },
  {
    id: 5,
    title: '传统文化故事动画',
    description: '用动画的形式讲述中国传统文化故事，传承优秀文化，培养文化自信。',
    coverImage: '/images/xiaoluo-default.webp',
    author: {
      nickName: '刘小强',
      avatarUrl: '/images/xiaoluo-default.webp'
    },
    className: '二班',
    grade: '五年级',
    publishToClassTime: '2024-01-11T11:20:00Z',
    viewCount: 87
  },
  {
    id: 6,
    title: '音乐创作与演奏',
    description: '学生原创的钢琴曲目，展现了音乐天赋和创作能力，旋律优美动听。',
    coverImage: '/images/xiaoluo-default.webp',
    author: {
      nickName: '周小雨',
      avatarUrl: '/images/xiaoluo-default.webp'
    },
    className: '一班',
    grade: '四年级',
    publishToClassTime: '2024-01-10T08:30:00Z',
    viewCount: 142
  }
];

const formatPublishTime = (timeStr: string) => {
  const date = new Date(timeStr);
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  });
};

const ClassProjectsDemo = () => {
  const [selectedClassId, setSelectedClassId] = useState<string | null>(null);
  const [searchKeyword, setSearchKeyword] = useState('');

  // 获取所有班级信息
  const allClasses = useMemo(() => {
    const classSet = new Set();
    const classes: Array<{id: string, grade: string, className: string}> = [];

    demoProjects.forEach(project => {
      const classKey = `${project.grade}-${project.className}`;
      if (!classSet.has(classKey)) {
        classSet.add(classKey);
        classes.push({
          id: classKey,
          grade: project.grade,
          className: project.className
        });
      }
    });

    return classes.sort((a, b) => a.grade.localeCompare(b.grade) || a.className.localeCompare(b.className));
  }, []);

  // 筛选项目
  const filteredProjects = useMemo(() => {
    let filtered = demoProjects;

    // 按班级筛选
    if (selectedClassId) {
      const [grade, className] = selectedClassId.split('-');
      filtered = filtered.filter(project =>
        project.grade === grade && project.className === className
      );
    }

    // 按关键词搜索
    if (searchKeyword) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(keyword) ||
        project.description.toLowerCase().includes(keyword) ||
        project.author.nickName.toLowerCase().includes(keyword)
      );
    }

    return filtered;
  }, [selectedClassId, searchKeyword]);

  const handleClassChange = (classId: string | null) => {
    setSelectedClassId(classId);
  };

  const handleSearch = (value: string) => {
    setSearchKeyword(value);
  };

  return (
    <div className={styles.container}>
      {/* 头部区域 */}
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <h2 className={styles.title}>
            <BookOpen className={styles.titleIcon} />
            班级项目 - 逻辑跃迁小学
          </h2>
          <p className={styles.subtitle}>
            查看和管理所有班级的学生项目作品 - 功能优化演示
          </p>
        </div>
      </div>

      {/* 筛选控制区域 */}
      <div className={styles.controls}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8} lg={6}>
            <div className={styles.controlItem}>
              <label className={styles.controlLabel}>选择班级:</label>
              <Select
                placeholder="全部班级"
                value={selectedClassId}
                onChange={handleClassChange}
                allowClear
                className={styles.classSelect}
              >
                <Option key="all" value={null}>
                  全部班级 ({demoProjects.length}个项目)
                </Option>
                {allClasses.map((classInfo) => {
                  const projectCount = demoProjects.filter(p =>
                    p.grade === classInfo.grade && p.className === classInfo.className
                  ).length;
                  return (
                    <Option key={classInfo.id} value={classInfo.id}>
                      {classInfo.grade} - {classInfo.className} ({projectCount}个项目)
                    </Option>
                  );
                })}
              </Select>
            </div>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <div className={styles.controlItem}>
              <label className={styles.controlLabel}>排序方式:</label>
              <Select
                defaultValue="newest"
                className={styles.orderSelect}
              >
                <Option value="newest">最新发布</Option>
                <Option value="oldest">最早发布</Option>
                <Option value="popular">最受欢迎</Option>
              </Select>
            </div>
          </Col>

          <Col xs={24} sm={24} md={8} lg={12}>
            <div className={styles.controlItem}>
              <SearchInput
                placeholder="搜索项目标题、描述或作者..."
                allowClear
                onSearch={handleSearch}
                onChange={(e) => {
                  if (!e.target.value) {
                    handleSearch('');
                  }
                }}
                className={styles.searchInput}
                prefix={<Search size={16} />}
              />
            </div>
          </Col>
        </Row>
      </div>

      {/* 内容区域 */}
      <div className={styles.content}>
        <div className={styles.projectGrid}>
          {filteredProjects.map((project) => (
            <Card
              key={project.id}
              className={styles.projectCard}
              hoverable
              cover={
                <div className={styles.projectCover}>
                  <Image
                    src={project.coverImage}
                    alt={project.title}
                    width={300}
                    height={200}
                    className={styles.projectImage}
                  />
                  <div className={styles.projectOverlay}>
                    <Button
                      type="primary"
                      icon={<Eye size={16} />}
                      className={styles.viewButton}
                    >
                      查看项目
                    </Button>
                  </div>
                </div>
              }
            >
              {/* 项目信息 */}
              <div className={styles.projectInfo}>
                <h3 className={styles.projectTitle} title={project.title}>
                  {project.title}
                </h3>

                <p className={styles.projectDescription} title={project.description}>
                  {project.description}
                </p>

                {/* 作者和班级信息 */}
                <div className={styles.projectMeta}>
                  <div className={styles.authorInfo}>
                    <Avatar
                      size={32}
                      src={project.author.avatarUrl}
                      className={styles.authorAvatar}
                    >
                      {project.author.nickName[0]}
                    </Avatar>
                    <span className={styles.authorName}>
                      {project.author.nickName}
                    </span>
                  </div>

                  {/* 班级信息 */}
                  <div className={styles.classInfo}>
                    <div className={styles.classTag}>
                      {project.grade} - {project.className}
                    </div>
                  </div>
                </div>

                {/* 底部信息 */}
                <div className={styles.projectFooter}>
                  <div className={styles.publishTime}>
                    <Calendar size={14} />
                    <span>{formatPublishTime(project.publishToClassTime)}</span>
                  </div>

                  <div className={styles.viewCount}>
                    <Eye size={14} />
                    <span>{project.viewCount}</span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ClassProjectsDemo;
