'use client';

import React from 'react';
import { Card, Avatar, Button } from 'antd';
import { Eye, Calendar, BookOpen } from 'lucide-react';
import Image from 'next/image';
import styles from './ClassProjects.module.css';

// 演示数据
const demoProjects = [
  {
    id: 1,
    title: '小洛机器人编程大赛作品',
    description: '使用Scratch编程制作的智能机器人，能够自动避障和语音交互，展示了学生们的创新思维和编程能力。',
    coverImage: '/images/xiaoluo-default.webp',
    author: {
      nickName: '张小明',
      avatarUrl: '/images/xiaoluo-default.webp'
    },
    className: '五年级一班',
    grade: '五年级',
    publishToClassTime: '2024-01-15T10:30:00Z',
    viewCount: 128
  },
  {
    id: 2,
    title: '环保主题创意设计',
    description: '关于环境保护的创意海报设计，运用了丰富的色彩和创新的构图，传达了保护地球的重要信息。',
    coverImage: '/images/xiaoluo-default.webp',
    author: {
      nickName: '李小红',
      avatarUrl: '/images/xiaoluo-default.webp'
    },
    className: '四年级二班',
    grade: '四年级',
    publishToClassTime: '2024-01-14T14:20:00Z',
    viewCount: 95
  },
  {
    id: 3,
    title: '数学几何动画演示',
    description: '通过动画的形式展示几何图形的变换过程，帮助同学们更好地理解数学概念，寓教于乐。',
    coverImage: '/images/xiaoluo-default.webp',
    author: {
      nickName: '王小华',
      avatarUrl: '/images/xiaoluo-default.webp'
    },
    className: '六年级三班',
    grade: '六年级',
    publishToClassTime: '2024-01-13T09:15:00Z',
    viewCount: 156
  }
];

const formatPublishTime = (timeStr: string) => {
  const date = new Date(timeStr);
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  });
};

const ClassProjectsDemo = () => {
  return (
    <div className={styles.container}>
      {/* 头部区域 */}
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <h2 className={styles.title}>
            <BookOpen className={styles.titleIcon} />
            班级项目 - 逻辑跃迁小学
          </h2>
          <p className={styles.subtitle}>
            查看和管理所有班级的学生项目作品 - 设计优化演示
          </p>
        </div>
      </div>

      {/* 内容区域 */}
      <div className={styles.content}>
        <div className={styles.projectGrid}>
          {demoProjects.map((project) => (
            <Card
              key={project.id}
              className={styles.projectCard}
              hoverable
              cover={
                <div className={styles.projectCover}>
                  <Image
                    src={project.coverImage}
                    alt={project.title}
                    width={300}
                    height={200}
                    className={styles.projectImage}
                  />
                  <div className={styles.projectOverlay}>
                    <Button
                      type="primary"
                      icon={<Eye size={16} />}
                      className={styles.viewButton}
                    >
                      查看项目
                    </Button>
                  </div>
                </div>
              }
            >
              {/* 项目信息 */}
              <div className={styles.projectInfo}>
                <h3 className={styles.projectTitle} title={project.title}>
                  {project.title}
                </h3>

                <p className={styles.projectDescription} title={project.description}>
                  {project.description}
                </p>

                {/* 作者和班级信息 */}
                <div className={styles.projectMeta}>
                  <div className={styles.authorInfo}>
                    <Avatar
                      size={32}
                      src={project.author.avatarUrl}
                      className={styles.authorAvatar}
                    >
                      {project.author.nickName[0]}
                    </Avatar>
                    <span className={styles.authorName}>
                      {project.author.nickName}
                    </span>
                  </div>

                  {/* 班级信息 */}
                  <div className={styles.classInfo}>
                    <div className={styles.classTag}>
                      {project.grade} - {project.className}
                    </div>
                  </div>
                </div>

                {/* 底部信息 */}
                <div className={styles.projectFooter}>
                  <div className={styles.publishTime}>
                    <Calendar size={14} />
                    <span>{formatPublishTime(project.publishToClassTime)}</span>
                  </div>

                  <div className={styles.viewCount}>
                    <Eye size={14} />
                    <span>{project.viewCount}</span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ClassProjectsDemo;
